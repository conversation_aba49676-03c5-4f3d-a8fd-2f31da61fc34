const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testExamStart() {
  try {
    console.log('🔍 Testing exam start functionality...')
    
    // Find the Grade 9 Mathematics Demo Exam
    const exam = await prisma.exam.findFirst({
      where: {
        title: 'Grade 9 Mathematics Demo Exam'
      },
      include: {
        class: true,
        subject: true,
        questions: {
          include: {
            question: true
          }
        }
      }
    })

    if (!exam) {
      console.log('❌ Exam not found')
      return
    }

    console.log('📋 Exam Details:')
    console.log(`   ID: ${exam.id}`)
    console.log(`   Title: ${exam.title}`)
    console.log(`   Class: ${exam.class.name}`)
    console.log(`   Subject: ${exam.subject.name}`)
    console.log(`   Active: ${exam.isActive}`)
    console.log(`   Duration: ${exam.duration} minutes`)
    console.log(`   Questions: ${exam.questions.length}`)
    console.log(`   Allow Retake: ${exam.allowRetake}`)
    console.log(`   Start Time: ${exam.startTime || 'None'}`)
    console.log(`   End Time: ${exam.endTime || 'None'}`)

    // Find a Grade 9 student
    const student = await prisma.student.findFirst({
      where: {
        class: {
          name: 'Grade 9'
        }
      },
      include: {
        class: true
      }
    })

    if (!student) {
      console.log('❌ No Grade 9 student found')
      return
    }

    console.log('\n👤 Student Details:')
    console.log(`   ID: ${student.id}`)
    console.log(`   Name: ${student.firstName} ${student.lastName}`)
    console.log(`   Class: ${student.class.name}`)
    console.log(`   Email: ${student.email}`)

    // Check existing sessions
    const existingSessions = await prisma.examSession.findMany({
      where: {
        examId: exam.id,
        studentId: student.id
      }
    })

    console.log('\n📝 Existing Sessions:')
    if (existingSessions.length === 0) {
      console.log('   No existing sessions')
    } else {
      existingSessions.forEach((session, index) => {
        console.log(`   Session ${index + 1}:`)
        console.log(`     ID: ${session.id}`)
        console.log(`     Status: ${session.status}`)
        console.log(`     Submitted: ${session.isSubmitted}`)
        console.log(`     Start Time: ${session.startTime}`)
        console.log(`     End Time: ${session.endTime || 'None'}`)
      })
    }

    // Clean up any existing sessions for testing
    if (existingSessions.length > 0) {
      console.log('\n🧹 Cleaning up existing sessions for testing...')
      await prisma.examSession.deleteMany({
        where: {
          examId: exam.id,
          studentId: student.id
        }
      })
      console.log('   ✅ Sessions cleaned up')
    }

    // Test session creation
    console.log('\n🧪 Testing session creation...')
    try {
      const newSession = await prisma.examSession.create({
        data: {
          examId: exam.id,
          studentId: student.id,
          startTime: new Date(),
          status: 'IN_PROGRESS',
          warningCount: 0
        }
      })

      console.log('   ✅ Session created successfully!')
      console.log(`   Session ID: ${newSession.id}`)
      console.log(`   Status: ${newSession.status}`)

      // Clean up test session
      await prisma.examSession.delete({
        where: { id: newSession.id }
      })
      console.log('   ✅ Test session cleaned up')

    } catch (sessionError) {
      console.log('   ❌ Session creation failed:')
      console.log(`   Error: ${sessionError.message}`)
      console.log(`   Code: ${sessionError.code}`)
    }

    console.log('\n🎉 Exam start test completed!')

  } catch (error) {
    console.error('❌ Error testing exam start:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testExamStart()
