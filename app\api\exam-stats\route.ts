import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month'

    // Calculate date range based on period
    const now = new Date()
    let startDate: Date

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'semester':
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
        break
      default: // month
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get total exams count
    const totalExams = await prisma.exam.count()

    // Get active exams count
    const activeExams = await prisma.exam.count({
      where: {
        isActive: true,
        OR: [
          { endTime: null },
          { endTime: { gte: now } }
        ]
      }
    })

    // Get total students count (approximate from classes)
    const totalStudents = await prisma.student.count()

    // Get recent exam results for average score calculation
    const recentResults = await prisma.examResult.findMany({
      where: {
        submittedAt: {
          gte: startDate
        }
      },
      select: {
        percentage: true,
        isPassed: true
      }
    })

    const averageScore = recentResults.length > 0
      ? recentResults.reduce((sum, result) => sum + result.percentage, 0) / recentResults.length
      : 0

    const completionRate = recentResults.length > 0
      ? (recentResults.filter(result => result.isPassed).length / recentResults.length) * 100
      : 0

    // Get recent exams with details
    const recentExams = await prisma.exam.findMany({
      where: {
        createdAt: {
          gte: startDate
        }
      },
      include: {
        class: {
          select: {
            name: true
          }
        },
        subject: {
          select: {
            name: true
          }
        },
        sessions: {
          select: {
            id: true,
            isSubmitted: true
          }
        },
        results: {
          select: {
            percentage: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    })

    // Get upcoming exams
    const upcomingExams = await prisma.exam.findMany({
      where: {
        isActive: true,
        startTime: {
          gte: now
        }
      },
      include: {
        class: {
          select: {
            name: true
          }
        },
        subject: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        startTime: 'asc'
      },
      take: 5
    })

    // Process recent exams data
    const processedRecentExams = recentExams.map(exam => {
      const totalSessions = exam.sessions.length
      const completedSessions = exam.sessions.filter(session => session.isSubmitted).length
      const averageExamScore = exam.results.length > 0
        ? exam.results.reduce((sum, result) => sum + result.percentage, 0) / exam.results.length
        : 0

      let status: 'active' | 'completed' | 'scheduled'
      if (exam.endTime && exam.endTime < now) {
        status = 'completed'
      } else if (exam.startTime && exam.startTime > now) {
        status = 'scheduled'
      } else {
        status = 'active'
      }

      return {
        id: exam.id,
        title: exam.title,
        subject: exam.subject?.name || 'Unknown',
        class: exam.class?.name || 'Unknown',
        studentsAttempted: completedSessions,
        totalStudents: totalSessions || 0,
        averageScore: Math.round(averageExamScore * 100) / 100,
        status,
        createdAt: exam.createdAt.toISOString()
      }
    })

    // Process upcoming exams data
    const processedUpcomingExams = upcomingExams.map(exam => ({
      id: exam.id,
      title: exam.title,
      subject: exam.subject?.name || 'Unknown',
      class: exam.class?.name || 'Unknown',
      startTime: exam.startTime?.toISOString() || '',
      duration: exam.duration
    }))

    const stats = {
      totalExams,
      activeExams,
      totalStudents,
      averageScore: Math.round(averageScore * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
      recentExams: processedRecentExams,
      upcomingExams: processedUpcomingExams
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Error fetching exam statistics:', error)
    
    // Return mock data in case of error
    const mockStats = {
      totalExams: 0,
      activeExams: 0,
      totalStudents: 0,
      averageScore: 0,
      completionRate: 0,
      recentExams: [],
      upcomingExams: []
    }

    return NextResponse.json(mockStats)
  }
}
