'use client'

import { useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Loader2,
  HelpCircle,
  BookOpen,
  Target,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'
import Link from 'next/link'

export default function ViewQuestionPage() {
  const router = useRouter()
  const params = useParams()
  const questionId = params.id as string

  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch question data
  const { data: questionData, isLoading: questionLoading, error: questionError, refetch } = useQuery({
    queryKey: ['question', questionId],
    queryFn: async () => {
      const response = await fetch(`/api/questions/${questionId}`)
      if (!response.ok) throw new Error('Failed to fetch question')
      return response.json()
    }
  })

  const handleDelete = async () => {
    if (!questionData?.question) return

    const confirmMessage = questionData.question.usageCount > 0
      ? `This question is currently used in ${questionData.question.usageCount} exam(s). You cannot delete it until it's removed from all exams.`
      : `Are you sure you want to delete this question? This action cannot be undone.`

    if (questionData.question.usageCount > 0) {
      alert(confirmMessage)
      return
    }

    if (!confirm(confirmMessage)) return

    try {
      setDeleting(true)
      setError(null)

      const response = await fetch(`/api/questions/${questionId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete question')
      }

      // Redirect to questions list
      router.push('/dashboard/online-exam/questions')

    } catch (error: any) {
      console.error('Error deleting question:', error)
      setError(error.message || 'Failed to delete question')
    } finally {
      setDeleting(false)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'HARD': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getQuestionTypeColor = (type: string) => {
    switch (type) {
      case 'MULTIPLE_CHOICE': return 'bg-blue-100 text-blue-800'
      case 'TRUE_FALSE': return 'bg-purple-100 text-purple-800'
      case 'FILL_BLANK': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatQuestionType = (type: string) => {
    return type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  if (questionLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    )
  }

  if (questionError || !questionData?.question) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/online-exam/questions">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Questions
              </Button>
            </Link>
          </div>
          <Alert>
            <AlertDescription>
              {questionError ? 'Failed to load question' : 'Question not found'}
            </AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    )
  }

  const question = questionData.question

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/online-exam/questions">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Questions
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Question Details
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                View and manage question information
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Link href={`/dashboard/online-exam/questions/${questionId}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={handleDelete}
              disabled={deleting}
              className="text-red-600 hover:text-red-700"
            >
              {deleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete
            </Button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <Alert>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Question Overview */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <HelpCircle className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-xl">Question Overview</CardTitle>
                  <CardDescription>
                    Created on {new Date(question.createdAt).toLocaleDateString()}
                    {question.updatedAt !== question.createdAt && (
                      <span> • Updated on {new Date(question.updatedAt).toLocaleDateString()}</span>
                    )}
                  </CardDescription>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge className={getDifficultyColor(question.difficulty)}>
                  {question.difficulty}
                </Badge>
                <Badge className={getQuestionTypeColor(question.questionType)}>
                  {formatQuestionType(question.questionType)}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Question Text */}
            <div>
              <h3 className="font-semibold text-lg mb-3">Question:</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-900 leading-relaxed">{question.questionText}</p>
              </div>
            </div>

            {/* Question Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                <BookOpen className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Subject</p>
                  <p className="font-medium">{question.subject.name}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <Target className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Marks</p>
                  <p className="font-medium">{question.marks} point{question.marks !== 1 ? 's' : ''}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                <Calendar className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Usage</p>
                  <p className="font-medium">{question.usageCount} exam{question.usageCount !== 1 ? 's' : ''}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Answer Options/Details */}
        <Card>
          <CardHeader>
            <CardTitle>Answer Details</CardTitle>
            <CardDescription>
              Correct answer and available options
            </CardDescription>
          </CardHeader>
          <CardContent>
            {question.questionType === 'MULTIPLE_CHOICE' && (
              <div className="space-y-3">
                <h4 className="font-medium">Options:</h4>
                {question.options.map((option: any, index: number) => (
                  <div
                    key={option.key}
                    className={`flex items-center space-x-3 p-3 rounded-lg border ${
                      option.key === question.correctAnswer
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    {option.key === question.correctAnswer ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-400" />
                    )}
                    <span className="font-medium">{option.key}.</span>
                    <span className={option.key === question.correctAnswer ? 'text-green-900' : 'text-gray-700'}>
                      {option.text}
                    </span>
                    {option.key === question.correctAnswer && (
                      <Badge className="bg-green-100 text-green-800 ml-auto">Correct</Badge>
                    )}
                  </div>
                ))}
              </div>
            )}

            {question.questionType === 'TRUE_FALSE' && (
              <div className="space-y-3">
                <h4 className="font-medium">Correct Answer:</h4>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-900">{question.correctAnswer}</span>
                  <Badge className="bg-green-100 text-green-800">Correct</Badge>
                </div>
              </div>
            )}

            {question.questionType === 'FILL_BLANK' && (
              <div className="space-y-3">
                <h4 className="font-medium">Correct Answer:</h4>
                <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg border border-green-200">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-900">{question.correctAnswer}</span>
                  <Badge className="bg-green-100 text-green-800 ml-auto">Correct</Badge>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Explanation */}
        {question.explanation && (
          <Card>
            <CardHeader>
              <CardTitle>Explanation</CardTitle>
              <CardDescription>
                Additional explanation for the correct answer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-900 leading-relaxed">{question.explanation}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Usage Warning */}
        {question.usageCount > 0 && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-amber-900">Question in Use</h4>
                  <p className="text-amber-800 mt-1">
                    This question is currently being used in {question.usageCount} exam{question.usageCount !== 1 ? 's' : ''}. 
                    Be careful when making changes as they will affect existing exams.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
