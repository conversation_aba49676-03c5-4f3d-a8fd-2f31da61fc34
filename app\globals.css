@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Day Picker Styles */
@import 'react-day-picker/dist/style.css';

/* Import mobile-responsive utilities */
@import './styles/mobile-responsive.css';

/* Custom styles for select inside dialog */
.custom-select-wrapper [data-radix-select-viewport] {
  position: relative;
  z-index: 100;
}

@layer base {
  :root {
    /* Base theme variables */
    --color-primary: #0070f3;
    --color-on-primary: #ffffff;
    --color-secondary: #6c757d;
    --color-on-secondary: #ffffff;
    --color-bg: #ffffff;
    --color-surface: #f8f9fa;
    --color-text: #212529;
    --color-border: #dee2e6;
    --color-error: #dc3545;
    --color-success: #28a745;
    --color-warning: #ffc107;
    --color-info: #17a2b8;

    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
    --font-size-base: 16px;
    --line-height: 1.5;

    /* Border radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-pill: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* Focus ring */
    --focus-ring-color: var(--color-primary);
    --focus-ring-width: 3px;
    --focus-ring-offset: 2px;

    /* Compatibility with shadcn/ui */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;

    /* Dynamic App Settings */
    --sidebar-bg-color: #1f2937;
    --sidebar-text-color: #ffffff;
  }

  /* Dark theme */
  [data-theme="dark"] {
    --color-primary: #3b82f6;
    --color-on-primary: #ffffff;
    --color-secondary: #6b7280;
    --color-on-secondary: #ffffff;
    --color-bg: #121212;
    --color-surface: #1e1e1e;
    --color-text: #e5e7eb;
    --color-border: #374151;
    --color-error: #ef4444;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-info: #3b82f6;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
    --focus-ring-color: #3b82f6;

    /* Ensure sidebar text is white in dark mode */
    --sidebar-text-color: #ffffff;
  }

  /* Solarized theme */
  [data-theme="solarized"] {
    --color-primary: #268bd2;
    --color-on-primary: #fdf6e3;
    --color-secondary: #2aa198;
    --color-on-secondary: #fdf6e3;
    --color-bg: #fdf6e3;
    --color-surface: #eee8d5;
    --color-text: #657b83;
    --color-border: #93a1a1;
    --color-error: #dc322f;
    --color-success: #859900;
    --color-warning: #b58900;
    --color-info: #268bd2;
    --shadow-sm: 0 1px 2px rgba(0, 43, 54, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 43, 54, 0.15);
    --shadow-lg: 0 10px 15px rgba(0, 43, 54, 0.2);
    --focus-ring-color: #268bd2;
  }

  /* High-contrast theme */
  [data-theme="high-contrast"] {
    --color-primary: #0000ff;
    --color-on-primary: #ffffff;
    --color-secondary: #000000;
    --color-on-secondary: #ffffff;
    --color-bg: #ffffff;
    --color-surface: #f0f0f0;
    --color-text: #000000;
    --color-border: #000000;
    --color-error: #ff0000;
    --color-success: #008000;
    --color-warning: #ff8000;
    --color-info: #0000ff;
    --font-size-base: 18px;
    --line-height: 1.6;
    --radius-sm: 2px;
    --radius-md: 4px;
    --radius-lg: 8px;
    --shadow-sm: 0 0 0 2px #000000;
    --shadow-md: 0 0 0 3px #000000;
    --shadow-lg: 0 0 0 4px #000000;
    --focus-ring-color: #000000;
    --focus-ring-width: 4px;
  }

  /* For compatibility with shadcn/ui dark mode */
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Ensure sidebar text is white in dark mode */
    --sidebar-text-color: #ffffff;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-color: var(--color-bg);
    color: var(--color-text);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height);
  }

  /* Focus styles */
  :focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
  }

  /* Theme transition */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  [data-reduced-motion="true"] .theme-transition {
    transition: none !important;
  }

  [data-reduced-motion="true"] * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Sidebar Styles */
.sidebar {
  @apply fixed top-0 left-0 h-screen w-64 transition-all duration-300 ease-in-out z-50 flex flex-col;
  background-color: var(--color-surface);
  color: var(--color-text);
  border-right: 1px solid var(--color-border);
}

.sidebar.collapsed {
  @apply w-20;
}

.sidebar nav {
  @apply flex-1 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

.sidebar nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar nav::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 20px;
}

.sidebar-item {
  @apply flex items-center px-4 py-3 cursor-pointer transition-colors duration-200;
  color: var(--color-text);
}

.sidebar-item:hover {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
}

.sidebar-item.active {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
}

.sidebar-icon {
  @apply mr-3 flex-shrink-0;
}

.sidebar-text {
  @apply text-sm font-medium;
}

/* Dark mode sidebar text styling */
[data-theme="dark"] .sidebar-text,
.dark .sidebar-text {
  color: #ffffff !important;
}

[data-theme="dark"] .sidebar-item,
.dark .sidebar-item {
  color: #ffffff !important;
}

[data-theme="dark"] .sidebar-subitem,
.dark .sidebar-subitem {
  color: #ffffff !important;
}

/* Sidebar Accordion Styles */
.sidebar-accordion {
  @apply w-full;
}

.sidebar-accordion [data-radix-accordion-content] {
  padding: 0;
  animation: none !important;
}

.accordion-trigger {
  @apply flex items-center justify-between;
}

.sidebar-subitem {
  @apply flex items-center px-4 py-2 cursor-pointer transition-colors duration-200 pl-8;
  color: var(--color-text);
}

.sidebar-subitem:hover {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
}

.sidebar-subitem.active {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
}

.sidebar-subicon {
  @apply mr-3 flex-shrink-0;
}

.accordion-content {
  @apply py-0;
}

.collapsed-accordion .sidebar-item {
  @apply justify-center;
}

/* Mobile Sidebar */
@media (max-width: 768px) {
  .sidebar {
    @apply -translate-x-full;
  }

  .sidebar.mobile-open {
    @apply translate-x-0;
  }
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  /* Improve touch targets */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile */
  .space-y-2 > * + * {
    margin-top: 0.75rem;
  }

  .space-x-2 > * + * {
    margin-left: 0.75rem;
  }

  /* Responsive text sizes */
  h1 {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  h2 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Better form controls */
  input, select, textarea {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Improved table scrolling */
  .table-container {
    -webkit-overflow-scrolling: touch;
  }
}

/* Main Content */
.main-content {
  @apply ml-64 transition-all duration-300 ease-in-out;
  background-color: var(--color-bg);
  color: var(--color-text);
}

.main-content.sidebar-collapsed {
  @apply ml-20;
}

@media (max-width: 768px) {
  .main-content {
    @apply ml-0;
  }

  /* Reduce padding on mobile */
  .main-content > div {
    padding: 1rem;
  }
}

/* Dashboard Layout */
.dashboard-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6;
}

.dashboard-card {
  background-color: var(--color-surface);
  color: var(--color-text);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--color-border);
}

/* Form Styles */
.form-container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding: var(--spacing-lg);
}

.form-section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--color-surface);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Table Styles */
.table-container {
  width: 100%;
  overflow-x: auto;
  background-color: var(--color-surface);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
}

.data-table {
  min-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.data-table th,
.data-table td {
  border-bottom: 1px solid var(--color-border);
}

.table-header {
  background-color: var(--color-surface);
  color: var(--color-text);
  font-weight: 600;
}

.table-row:hover {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
}

.table-cell {
  padding: var(--spacing-md) var(--spacing-lg);
  white-space: nowrap;
  font-size: 0.875rem;
}

/* Button Styles */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: background-color 0.2s, box-shadow 0.2s;
}

.btn-primary:hover {
  filter: brightness(1.1);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-on-secondary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: background-color 0.2s, box-shadow 0.2s;
}

.btn-secondary:hover {
  filter: brightness(1.1);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background-color: var(--color-error);
  color: var(--color-on-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: background-color 0.2s, box-shadow 0.2s;
}

.btn-danger:hover {
  filter: brightness(1.1);
  box-shadow: var(--shadow-md);
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border-top: 2px solid var(--color-primary);
  border-bottom: 2px solid var(--color-primary);
  border-left: 2px solid transparent;
  border-right: 2px solid transparent;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Login Page Animations */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animated-gradient {
  background-size: 200% 200%;
  animation: gradientAnimation 15s ease infinite;
}

/* Enhanced Dialog Animations */
@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes dialogFadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.dialog-enter {
  animation: dialogFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.dialog-exit {
  animation: dialogFadeOut 0.2s cubic-bezier(0.4, 0, 1, 1);
}

.backdrop-enter {
  animation: backdropFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Enhanced Dialog Styles */
.enhanced-dialog {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.enhanced-dialog-backdrop {
  backdrop-filter: blur(12px);
  background: rgba(0, 0, 0, 0.6);
}

/* Ensure dialog is always on top */
.recursion-safe-dialog-container {
  z-index: 99999 !important;
  position: fixed !important;
  inset: 0 !important;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-600 {
  animation-delay: 0.6s;
}

.delay-700 {
  animation-delay: 0.7s;
}

@keyframes pulse-shadow {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
  }
}

.animate-pulse-shadow {
  animation: pulse-shadow 2s infinite;
}

/* Dashboard Card Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.dashboard-card-float {
  animation: float 4s ease-in-out infinite;
}

@keyframes scale-up {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

.hover-scale-up {
  transition: transform 0.3s ease-in-out;
}

.hover-scale-up:hover {
  transform: scale(1.05);
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(0, 112, 243, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 112, 243, 0.4);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 112, 243, 0.2);
  }
}

.hover-glow:hover {
  animation: glow 1.5s infinite;
}

/* Custom Scrollbar Styling */
.max-h-\[200px\].overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 112, 243, 0.3) transparent;
}

.max-h-\[200px\].overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[200px\].overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.max-h-\[200px\].overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(0, 112, 243, 0.3);
  border-radius: 20px;
}

.max-h-\[200px\].overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 112, 243, 0.5);
}