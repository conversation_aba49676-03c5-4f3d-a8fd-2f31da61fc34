'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { 
  Monitor, 
  Clock, 
  Shield, 
  CheckCircle, 
  Users, 
  BookOpen,
  ArrowRight,
  Lock,
  Timer,
  Award
} from 'lucide-react'
import Link from 'next/link'

export default function ExamLandingPage() {
  const router = useRouter()

  useEffect(() => {
    // Check if user is already logged in to exam system
    const examToken = localStorage.getItem('examToken')
    if (examToken) {
      // Verify token and redirect to exam selection
      router.push('/exam/select')
    }
  }, [router])

  const features = [
    {
      icon: Monitor,
      title: 'Online Examination',
      description: 'Take exams from anywhere with our secure online platform'
    },
    {
      icon: Clock,
      title: 'Timed Tests',
      description: 'Automatic time management with visual countdown timers'
    },
    {
      icon: Shield,
      title: 'Secure Environment',
      description: 'Anti-cheating measures and secure exam environment'
    },
    {
      icon: CheckCircle,
      title: 'Instant Results',
      description: 'Get your results immediately after exam completion'
    },
    {
      icon: Users,
      title: 'Multi-Class Support',
      description: 'Exams organized by class and subject for easy access'
    },
    {
      icon: BookOpen,
      title: 'Question Bank',
      description: 'Comprehensive question database with various difficulty levels'
    }
  ]

  const examProcess = [
    {
      step: 1,
      title: 'Login',
      description: 'Enter your Student ID and password to access the exam system',
      icon: Lock
    },
    {
      step: 2,
      title: 'Select Exam',
      description: 'Choose from available exams for your class and subjects',
      icon: BookOpen
    },
    {
      step: 3,
      title: 'Read Instructions',
      description: 'Carefully review exam instructions and requirements',
      icon: CheckCircle
    },
    {
      step: 4,
      title: 'Take Exam',
      description: 'Answer questions within the allocated time limit',
      icon: Timer
    },
    {
      step: 5,
      title: 'Submit & Results',
      description: 'Submit your exam and view your results instantly',
      icon: Award
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Monitor className="h-8 w-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Online Exam System</h1>
            </div>
            <Link href="/website">
              <Button variant="outline">
                Back to Website
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to Online Examination Portal
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            A secure, reliable, and user-friendly platform for conducting online examinations. 
            Access your exams anytime, anywhere with our advanced examination system.
          </p>
          <div className="flex justify-center space-x-4">
            <Link href="/exam/login">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <Users className="h-5 w-5 mr-2" />
                Student Login
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
            </Link>
            <Link href="/dashboard/online-exam">
              <Button variant="outline" size="lg">
                <Shield className="h-5 w-5 mr-2" />
                Admin Dashboard
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Key Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <feature.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* How It Works */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            How It Works
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {examProcess.map((process, index) => (
              <div key={index} className="text-center">
                <div className="relative">
                  <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                    <process.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute top-8 left-16 w-full h-0.5 bg-blue-200 hidden md:block">
                    {index < examProcess.length - 1 && (
                      <div className="w-full h-full bg-blue-600"></div>
                    )}
                  </div>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  Step {process.step}: {process.title}
                </h4>
                <p className="text-sm text-gray-600">
                  {process.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Security Notice */}
        <Card className="bg-yellow-50 border-yellow-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-yellow-800">
              <Shield className="h-5 w-5" />
              <span>Security & Fair Play</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-yellow-700 space-y-2">
              <p>
                <strong>Important:</strong> Our examination system includes advanced security measures to ensure fair play:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Tab switching and window focus monitoring</li>
                <li>Fullscreen mode enforcement during exams</li>
                <li>Automatic session timeout and submission</li>
                <li>Copy/paste and right-click restrictions</li>
                <li>Real-time violation tracking</li>
              </ul>
              <p className="mt-3">
                Please ensure you have a stable internet connection and avoid any activities that might be flagged as violations.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* System Requirements */}
        <div className="mt-12">
          <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
            System Requirements
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recommended Browser</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-gray-600">
                  <li>• Google Chrome (latest version)</li>
                  <li>• Mozilla Firefox (latest version)</li>
                  <li>• Microsoft Edge (latest version)</li>
                  <li>• Safari (latest version)</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Technical Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-gray-600">
                  <li>• Stable internet connection</li>
                  <li>• JavaScript enabled</li>
                  <li>• Cookies enabled</li>
                  <li>• Screen resolution: 1024x768 or higher</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Start Your Exam?
          </h3>
          <p className="text-gray-600 mb-6">
            Log in with your student credentials to access available examinations.
          </p>
          <Link href="/exam/login">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              <Users className="h-5 w-5 mr-2" />
              Access Exam Portal
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
