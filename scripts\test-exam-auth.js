// Simple test script to verify exam authentication
const testExamAuth = async () => {
  const baseUrl = 'http://localhost:3000'
  
  console.log('🧪 Testing Exam Authentication...')
  console.log('📋 Test Credentials:')
  console.log('   Student ID: 2024001')
  console.log('   Password: 2024001123')
  console.log('')

  try {
    // Test login
    console.log('1️⃣ Testing student login...')
    const loginResponse = await fetch(`${baseUrl}/api/exam/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sid: '2024001',
        password: '2024001123'
      })
    })

    const loginData = await loginResponse.json()
    
    if (loginResponse.ok && loginData.success) {
      console.log('✅ Login successful!')
      console.log(`   Student: ${loginData.student.name}`)
      console.log(`   Class: ${loginData.student.className}`)
      console.log(`   Token: ${loginData.token.substring(0, 20)}...`)
      
      // Test token verification
      console.log('')
      console.log('2️⃣ Testing token verification...')
      const verifyResponse = await fetch(`${baseUrl}/api/exam/auth`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginData.token}`
        }
      })

      const verifyData = await verifyResponse.json()
      
      if (verifyResponse.ok && verifyData.success) {
        console.log('✅ Token verification successful!')
        console.log(`   Verified student: ${verifyData.student.name}`)
      } else {
        console.log('❌ Token verification failed!')
        console.log('   Error:', verifyData.message)
      }

      // Test available exams
      console.log('')
      console.log('3️⃣ Testing available exams...')
      const examsResponse = await fetch(`${baseUrl}/api/exam/available`, {
        headers: {
          'Authorization': `Bearer ${loginData.token}`
        }
      })

      const examsData = await examsResponse.json()
      
      if (examsResponse.ok) {
        console.log('✅ Available exams retrieved!')
        console.log(`   Found ${examsData.exams?.length || 0} exam(s)`)
        if (examsData.exams && examsData.exams.length > 0) {
          examsData.exams.forEach((exam, index) => {
            console.log(`   ${index + 1}. ${exam.title} (${exam.duration} min, ${exam.totalMarks} marks)`)
          })
        }
      } else {
        console.log('❌ Failed to retrieve available exams!')
        console.log('   Error:', examsData.message || examsData.error)
      }

    } else {
      console.log('❌ Login failed!')
      console.log('   Error:', loginData.message)
    }

  } catch (error) {
    console.log('💥 Test failed with error:', error.message)
    console.log('')
    console.log('🔧 Make sure:')
    console.log('   1. The development server is running (npm run dev)')
    console.log('   2. The database is connected')
    console.log('   3. Demo data has been seeded')
  }

  console.log('')
  console.log('🎯 Test completed!')
}

// Run the test
testExamAuth()
