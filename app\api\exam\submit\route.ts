import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import { SubmitExamRequest } from '@/app/types/exam'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

export async function POST(request: Request) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId } = studentData
    const body: SubmitExamRequest = await request.json()
    const { sessionId } = body

    console.log(`Submitting exam for session ${sessionId}`)

    // Verify session exists and belongs to student
    const session = await prisma.examSession.findFirst({
      where: {
        id: sessionId,
        studentId: studentId
      },
      include: {
        exam: {
          include: {
            questions: {
              include: {
                question: {
                  select: {
                    id: true,
                    correctAnswer: true,
                    marks: true
                  }
                }
              }
            }
          }
        },
        responses: true
      }
    })

    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 404 }
      )
    }

    if (session.isSubmitted) {
      return NextResponse.json(
        { error: 'Exam has already been submitted' },
        { status: 400 }
      )
    }

    const currentTime = new Date()

    // Calculate results
    let totalMarks = 0
    let marksObtained = 0
    let correctAnswers = 0
    let totalQuestions = session.exam.questions.length

    // Create response map for easy lookup
    const responseMap = session.responses.reduce((acc, response) => {
      acc[response.questionId] = response.selectedAnswer
      return acc
    }, {} as Record<string, string | null>)

    // Calculate marks for each question
    const questionResults = session.exam.questions.map(examQuestion => {
      const question = examQuestion.question
      const studentAnswer = responseMap[question.id]
      const isCorrect = studentAnswer === question.correctAnswer
      const marksForQuestion = isCorrect ? examQuestion.marks : 0

      totalMarks += examQuestion.marks
      marksObtained += marksForQuestion

      if (isCorrect) {
        correctAnswers++
      }

      return {
        questionId: question.id,
        studentAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        marks: examQuestion.marks,
        marksObtained: marksForQuestion
      }
    })

    const percentage = totalMarks > 0 ? (marksObtained / totalMarks) * 100 : 0
    const isPassed = marksObtained >= session.exam.passingMarks

    // Use transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Update session as submitted
      const updatedSession = await tx.examSession.update({
        where: { id: sessionId },
        data: {
          status: 'COMPLETED',
          isSubmitted: true,
          endTime: currentTime
        }
      })

      // Create exam result
      const examResult = await tx.examResult.create({
        data: {
          examId: session.examId,
          studentId: studentId,
          sessionId: sessionId,
          totalQuestions,
          attemptedQuestions: session.responses.length,
          correctAnswers,
          wrongAnswers: session.responses.length - correctAnswers,
          marksObtained,
          totalMarks,
          percentage,
          isPassed,
          timeTaken: Math.floor((currentTime.getTime() - session.startTime.getTime()) / 1000),
          submittedAt: currentTime
        }
      })

      return { session: updatedSession, result: examResult }
    })

    console.log(`Exam submitted successfully. Result ID: ${result.result.id}`)

    // Prepare response based on exam settings
    const response: any = {
      success: true,
      message: 'Exam submitted successfully',
      resultId: result.result.id,
      submittedAt: currentTime
    }

    // Include results if exam allows showing results
    if (session.exam.showResults) {
      response.results = {
        marksObtained,
        totalMarks,
        percentage: Math.round(percentage * 100) / 100,
        isPassed,
        correctAnswers,
        totalQuestions,
        timeTaken: result.result.timeTaken,
        grade: calculateGrade(percentage)
      }

      // Include question-wise results if needed
      response.questionResults = questionResults
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error submitting exam:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      sessionId: body?.sessionId,
      studentId: studentData?.studentId
    })
    return NextResponse.json(
      {
        error: 'Failed to submit exam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Helper function to calculate grade based on percentage
function calculateGrade(percentage: number): string {
  if (percentage >= 90) return 'A+'
  if (percentage >= 80) return 'A'
  if (percentage >= 70) return 'B+'
  if (percentage >= 60) return 'B'
  if (percentage >= 50) return 'C+'
  if (percentage >= 40) return 'C'
  if (percentage >= 33) return 'D'
  return 'F'
}

// GET - Get exam result
export async function GET(request: Request) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId } = studentData
    const { searchParams } = new URL(request.url)
    const resultId = searchParams.get('resultId')

    if (!resultId) {
      return NextResponse.json(
        { error: 'Result ID is required' },
        { status: 400 }
      )
    }

    // Get exam result
    const result = await prisma.examResult.findFirst({
      where: {
        id: resultId,
        studentId: studentId
      },
      include: {
        exam: {
          select: {
            id: true,
            title: true,
            showResults: true,
            subject: {
              select: {
                name: true
              }
            },
            class: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    if (!result) {
      return NextResponse.json(
        { error: 'Result not found' },
        { status: 404 }
      )
    }

    if (!result.exam.showResults) {
      return NextResponse.json(
        { error: 'Results are not available for this exam' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      result: {
        id: result.id,
        marksObtained: result.marksObtained,
        totalMarks: result.totalMarks,
        percentage: result.percentage,
        isPassed: result.isPassed,
        correctAnswers: result.correctAnswers,
        totalQuestions: result.totalQuestions,
        timeTaken: result.timeTaken,
        submittedAt: result.submittedAt,
        grade: calculateGrade(result.percentage),
        exam: result.exam
      }
    })

  } catch (error) {
    console.error('Error fetching exam result:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exam result' },
      { status: 500 }
    )
  }
}
