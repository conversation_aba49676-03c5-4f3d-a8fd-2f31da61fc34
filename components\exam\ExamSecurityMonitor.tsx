'use client'

import { useState, useEffect } from 'react'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { 
  Shield, 
  AlertTriangle, 
  Eye, 
  Maximize, 
  Clock,
  Activity,
  X
} from 'lucide-react'
import { useExamSecurity } from '@/hooks/useExamSecurity'

interface ExamSecurityMonitorProps {
  onSecurityViolation?: (violationType: string, count: number) => void
  onForceSubmit?: () => void
  maxViolationsBeforeSubmit?: number
  showSecurityStatus?: boolean
  className?: string
}

export default function ExamSecurityMonitor({
  onSecurityViolation,
  onForceSubmit,
  maxViolationsBeforeSubmit = 5,
  showSecurityStatus = true,
  className = ''
}: ExamSecurityMonitorProps) {
  const [showWarning, setShowWarning] = useState(false)
  const [warningMessage, setWarningMessage] = useState('')
  const [criticalViolation, setCriticalViolation] = useState(false)

  const {
    violations,
    warningCount,
    isFullscreen,
    tabSwitchCount,
    windowBlurCount,
    enterFullscreen,
    getSecurityReport
  } = useExamSecurity({
    maxTabSwitches: 3,
    maxWindowBlurs: 5,
    enforceFullscreen: true,
    disableRightClick: true,
    disableCopyPaste: true,
    disableDevTools: true,
    onSecurityViolation: (event) => {
      handleSecurityViolation(event.type, event.details || '')
    },
    onWarningThreshold: (count) => {
      if (count >= maxViolationsBeforeSubmit) {
        setCriticalViolation(true)
        setWarningMessage('Too many security violations detected. Your exam will be automatically submitted.')
        setTimeout(() => {
          onForceSubmit?.()
        }, 10000) // Give 10 seconds warning before auto-submit
      }
    }
  })

  const handleSecurityViolation = (type: string, details: string) => {
    onSecurityViolation?.(type, violations.length + 1)
    
    let message = ''
    switch (type) {
      case 'tab_switch':
        message = 'Tab switching detected. Please stay on the exam page.'
        break
      case 'window_blur':
        message = 'Window focus lost. Please keep the exam window active.'
        break
      case 'fullscreen_exit':
        message = 'Fullscreen mode exited. Please return to fullscreen.'
        break
      case 'right_click':
        message = 'Right-click is disabled during the exam.'
        break
      case 'copy_attempt':
        message = 'Copy/paste operations are not allowed.'
        break
      default:
        message = 'Security violation detected.'
    }

    setWarningMessage(message)
    setShowWarning(true)
    
    // Auto-hide warning after 5 seconds
    setTimeout(() => setShowWarning(false), 5000)
  }

  const getViolationSeverity = () => {
    if (criticalViolation) return 'critical'
    if (warningCount >= 3) return 'high'
    if (warningCount >= 1) return 'medium'
    return 'low'
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-300'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      default:
        return 'bg-green-100 text-green-800 border-green-300'
    }
  }

  const handleEnterFullscreen = () => {
    enterFullscreen()
    setShowWarning(false)
  }

  const handleDismissWarning = () => {
    setShowWarning(false)
  }

  return (
    <div className={className}>
      {/* Security Warning Alert */}
      {showWarning && (
        <Alert 
          variant={criticalViolation ? "destructive" : "default"} 
          className="mb-4 animate-pulse"
        >
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{warningMessage}</span>
            <div className="flex items-center space-x-2">
              {!isFullscreen && (
                <Button size="sm" onClick={handleEnterFullscreen}>
                  <Maximize className="h-3 w-3 mr-1" />
                  Fullscreen
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={handleDismissWarning}>
                <X className="h-3 w-3" />
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Critical Violation Warning */}
      {criticalViolation && (
        <Alert variant="destructive" className="mb-4 border-2 border-red-500">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <div className="font-bold">CRITICAL SECURITY VIOLATION</div>
            <div className="text-sm mt-1">
              Your exam will be automatically submitted in 10 seconds due to multiple security violations.
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Security Status Display */}
      {showSecurityStatus && (
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Security Status</span>
              <Badge className={getSeverityColor(getViolationSeverity())}>
                {getViolationSeverity().toUpperCase()}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
              <div className="flex items-center space-x-2">
                <Eye className="h-3 w-3 text-blue-600" />
                <span>Tab Switches: {tabSwitchCount}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Activity className="h-3 w-3 text-orange-600" />
                <span>Focus Lost: {windowBlurCount}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Maximize className={`h-3 w-3 ${isFullscreen ? 'text-green-600' : 'text-red-600'}`} />
                <span>Fullscreen: {isFullscreen ? 'ON' : 'OFF'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-3 w-3 text-red-600" />
                <span>Violations: {violations.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fullscreen Prompt */}
      {!isFullscreen && !showWarning && (
        <Alert className="mb-4">
          <Maximize className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>For the best exam experience, please enable fullscreen mode.</span>
            <Button size="sm" onClick={handleEnterFullscreen}>
              <Maximize className="h-3 w-3 mr-1" />
              Enable Fullscreen
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

// Security Report Component for Admin/Debug
export function SecurityReport({ 
  violations, 
  className = '' 
}: { 
  violations: any[]
  className?: string 
}) {
  const [showDetails, setShowDetails] = useState(false)

  if (violations.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 text-green-600">
            <Shield className="h-4 w-4" />
            <span className="text-sm">No security violations detected</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <span>Security Violations ({violations.length})</span>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? 'Hide' : 'Show'} Details
          </Button>
        </CardTitle>
      </CardHeader>
      {showDetails && (
        <CardContent>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {violations.map((violation, index) => (
              <div key={index} className="text-xs bg-gray-50 p-2 rounded">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{violation.type.replace('_', ' ').toUpperCase()}</span>
                  <span className="text-gray-500">
                    {new Date(violation.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                {violation.details && (
                  <div className="text-gray-600 mt-1">{violation.details}</div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  )
}
