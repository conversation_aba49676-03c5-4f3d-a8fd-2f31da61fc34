import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedExamDemo() {
  try {
    console.log('🌱 Seeding demo exam data...')

    // Create demo class if it doesn't exist
    const demoClass = await prisma.class.upsert({
      where: { name: 'Grade 9' },
      update: {},
      create: {
        name: 'Grade 9'
      }
    })

    // Create demo subject if it doesn't exist
    const demoSubject = await prisma.subject.upsert({
      where: {
        name_classId: {
          name: 'Mathematics',
          classId: demoClass.id
        }
      },
      update: {},
      create: {
        name: 'Mathematics',
        classId: demoClass.id
      }
    })

    // Create demo student if it doesn't exist
    const demoStudent = await prisma.student.upsert({
      where: { sid: '2024001' },
      update: {},
      create: {
        sid: '2024001',
        name: 'Demo Student',
        className: 'Grade 9',
        fatherName: 'Demo Father',
        gfName: 'De<PERSON> Grandfather',
        age: 15,
        gender: 'Male'
      }
    })

    // Create demo questions
    const questions = [
      {
        questionText: 'What is 2 + 2?',
        questionType: 'MULTIPLE_CHOICE',
        options: [
          { key: 'A', text: '3' },
          { key: 'B', text: '4' },
          { key: 'C', text: '5' },
          { key: 'D', text: '6' }
        ],
        correctAnswer: 'B',
        marks: 1,
        difficulty: 'EASY',
        explanation: '2 + 2 equals 4'
      },
      {
        questionText: 'What is the square root of 16?',
        questionType: 'MULTIPLE_CHOICE',
        options: [
          { key: 'A', text: '2' },
          { key: 'B', text: '3' },
          { key: 'C', text: '4' },
          { key: 'D', text: '8' }
        ],
        correctAnswer: 'C',
        marks: 2,
        difficulty: 'MEDIUM',
        explanation: 'The square root of 16 is 4'
      },
      {
        questionText: 'Solve for x: 2x + 5 = 13',
        questionType: 'MULTIPLE_CHOICE',
        options: [
          { key: 'A', text: 'x = 3' },
          { key: 'B', text: 'x = 4' },
          { key: 'C', text: 'x = 5' },
          { key: 'D', text: 'x = 6' }
        ],
        correctAnswer: 'B',
        marks: 3,
        difficulty: 'MEDIUM',
        explanation: '2x + 5 = 13, so 2x = 8, therefore x = 4'
      },
      {
        questionText: 'What is the value of π (pi) approximately?',
        questionType: 'MULTIPLE_CHOICE',
        options: [
          { key: 'A', text: '3.14' },
          { key: 'B', text: '2.71' },
          { key: 'C', text: '1.41' },
          { key: 'D', text: '1.73' }
        ],
        correctAnswer: 'A',
        marks: 1,
        difficulty: 'EASY',
        explanation: 'π (pi) is approximately 3.14159...'
      },
      {
        questionText: 'If a triangle has angles of 60°, 60°, and 60°, what type of triangle is it?',
        questionType: 'MULTIPLE_CHOICE',
        options: [
          { key: 'A', text: 'Right triangle' },
          { key: 'B', text: 'Equilateral triangle' },
          { key: 'C', text: 'Isosceles triangle' },
          { key: 'D', text: 'Scalene triangle' }
        ],
        correctAnswer: 'B',
        marks: 2,
        difficulty: 'EASY',
        explanation: 'A triangle with all angles equal (60°) is an equilateral triangle'
      }
    ]

    // Create questions in database
    const createdQuestions = []
    for (const questionData of questions) {
      const question = await prisma.question.create({
        data: {
          questionText: questionData.questionText,
          questionType: questionData.questionType as any,
          options: questionData.options,
          correctAnswer: questionData.correctAnswer,
          marks: questionData.marks,
          difficulty: questionData.difficulty as any,
          explanation: questionData.explanation,
          subjectId: demoSubject.id,
          classId: demoClass.id,
          createdBy: 'system'
        }
      })
      createdQuestions.push(question)
    }

    // Create demo exam
    const demoExam = await prisma.exam.create({
      data: {
        title: 'Grade 9 Mathematics Demo Exam',
        description: 'A comprehensive mathematics exam for Grade 9 students covering basic algebra and arithmetic',
        classId: demoClass.id,
        subjectId: demoSubject.id,
        duration: 30, // 30 minutes
        totalMarks: 9, // Sum of all question marks
        passingMarks: 5, // 55% to pass
        instructions: 'Read all questions carefully. Choose the best answer. You have 30 minutes to complete this exam.',
        isActive: true,
        startTime: new Date(Date.now() - 24 * 60 * 60 * 1000), // Started yesterday
        endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Ends in 7 days
        allowRetake: true,
        shuffleQuestions: false,
        showResults: true,
        createdBy: 'system'
      }
    })

    // Link questions to exam
    for (let i = 0; i < createdQuestions.length; i++) {
      await prisma.examQuestion.create({
        data: {
          examId: demoExam.id,
          questionId: createdQuestions[i].id,
          order: i + 1,
          marks: createdQuestions[i].marks
        }
      })
    }

    console.log('✅ Demo exam data seeded successfully!')
    console.log(`📚 Created exam: ${demoExam.title}`)
    console.log(`👨‍🎓 Demo student: ${demoStudent.name} (SID: ${demoStudent.sid})`)
    console.log(`🔑 Login credentials: SID = 2024001, Password = 2024001123`)
    console.log(`📝 Questions created: ${createdQuestions.length}`)
    console.log(`⏱️ Exam duration: ${demoExam.duration} minutes`)
    console.log(`🎯 Total marks: ${demoExam.totalMarks}`)
    console.log(`✅ Passing marks: ${demoExam.passingMarks}`)

  } catch (error) {
    console.error('❌ Error seeding demo exam data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
if (require.main === module) {
  seedExamDemo()
    .then(() => {
      console.log('🎉 Demo exam seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Demo exam seeding failed:', error)
      process.exit(1)
    })
}

export default seedExamDemo
