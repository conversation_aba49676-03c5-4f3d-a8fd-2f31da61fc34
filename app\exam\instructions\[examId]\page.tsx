'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Checkbox } from '@/app/components/ui/checkbox'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Loader2, Clock, FileText, Target, AlertTriangle, CheckCircle, ArrowLeft } from 'lucide-react'

interface ExamDetails {
  id: string
  title: string
  description?: string
  duration: number
  totalMarks: number
  passingMarks: number
  instructions?: string
  questionCount: number
  subject: {
    name: string
  }
  class: {
    name: string
  }
}

export default function ExamInstructionsPage() {
  const [exam, setExam] = useState<ExamDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isStarting, setIsStarting] = useState(false)
  const [error, setError] = useState('')
  const [agreedToInstructions, setAgreedToInstructions] = useState(false)
  const router = useRouter()
  const params = useParams()
  const examId = params.examId as string

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('examToken')
    if (!token) {
      router.push('/exam/login')
      return
    }

    fetchExamDetails(token)
  }, [examId, router])

  const fetchExamDetails = async (token: string) => {
    try {
      const response = await fetch(`/api/exams/${examId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setExam(data.exam)
      } else {
        setError('Failed to fetch exam details')
      }
    } catch (error) {
      console.error('Error fetching exam details:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleStartExam = async () => {
    if (!agreedToInstructions) {
      setError('Please read and agree to the instructions before starting the exam.')
      return
    }

    setIsStarting(true)
    setError('')

    try {
      const token = localStorage.getItem('examToken')
      const response = await fetch('/api/exam/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          examId: examId
        })
      })

      if (response.ok) {
        const data = await response.json()
        // Redirect to exam interface
        router.push(`/exam/take/${examId}?sessionId=${data.sessionId}`)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to start exam')
      }
    } catch (error) {
      console.error('Error starting exam:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsStarting(false)
    }
  }

  const handleGoBack = () => {
    router.push('/exam/select')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading exam details...</p>
        </div>
      </div>
    )
  }

  if (!exam) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Exam Not Found</h3>
            <p className="text-gray-600 mb-4">The requested exam could not be found.</p>
            <Button onClick={handleGoBack}>Go Back</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" onClick={handleGoBack} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exams
          </Button>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{exam.title}</h1>
          <p className="text-gray-600">{exam.subject.name} • {exam.class.name}</p>
          {exam.description && (
            <p className="text-gray-700 mt-2">{exam.description}</p>
          )}
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Exam Details */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Exam Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Duration</span>
                  </div>
                  <span className="font-medium">{exam.duration} minutes</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Questions</span>
                  </div>
                  <span className="font-medium">{exam.questionCount}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Total Marks</span>
                  </div>
                  <span className="font-medium">{exam.totalMarks}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Passing Marks</span>
                  </div>
                  <span className="font-medium">{exam.passingMarks}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Exam Instructions</CardTitle>
                <CardDescription>
                  Please read all instructions carefully before starting the exam
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Custom Instructions */}
                  {exam.instructions && (
                    <div className="prose prose-sm max-w-none">
                      <div className="whitespace-pre-wrap text-gray-700">
                        {exam.instructions}
                      </div>
                    </div>
                  )}

                  {/* Default Instructions */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">General Instructions:</h4>
                    <ul className="space-y-2 text-sm text-gray-700">
                      <li className="flex items-start space-x-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>All questions are mandatory and carry equal marks unless specified</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>You can navigate between questions using the question navigator</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>You can flag questions for review and come back to them later</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>Your answers are automatically saved as you progress</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>The exam will auto-submit when the time expires</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="text-red-600 mt-1">•</span>
                        <span>Do not refresh the page or close the browser during the exam</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="text-red-600 mt-1">•</span>
                        <span>Switching tabs or windows will be monitored and may result in warnings</span>
                      </li>
                    </ul>
                  </div>

                  {/* Warning */}
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Important:</strong> Any suspicious activity during the exam will be recorded. 
                      Ensure you have a stable internet connection before starting.
                    </AlertDescription>
                  </Alert>

                  {/* Agreement Checkbox */}
                  <div className="flex items-center space-x-2 pt-4">
                    <Checkbox
                      id="agree"
                      checked={agreedToInstructions}
                      onCheckedChange={(checked) => setAgreedToInstructions(checked as boolean)}
                    />
                    <label
                      htmlFor="agree"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      I have read and understood all the instructions above
                    </label>
                  </div>

                  {/* Start Button */}
                  <div className="pt-6">
                    <Button
                      onClick={handleStartExam}
                      disabled={!agreedToInstructions || isStarting}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      size="lg"
                    >
                      {isStarting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Starting Exam...
                        </>
                      ) : (
                        'Start Exam'
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
