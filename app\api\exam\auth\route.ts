import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { StudentLoginRequest, StudentLoginResponse } from '@/app/types/exam'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

export async function POST(request: Request) {
  try {
    const body: StudentLoginRequest = await request.json()
    const { sid, password } = body

    // Validate input
    if (!sid || !password) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Student ID and password are required' 
        } as StudentLoginResponse,
        { status: 400 }
      )
    }

    console.log(`Student login attempt for SID: ${sid}`)

    // Find student by SID
    let student = await prisma.student.findUnique({
      where: { sid },
      select: {
        id: true,
        name: true,
        sid: true,
        className: true,
        // Note: We'll need to add password field to Student model
        // For now, we'll use a default password logic
      }
    })

    // If student not found in database, create a demo student for testing
    if (!student && sid === '2024001') {
      // Create demo student for testing purposes
      student = {
        id: 'demo-student-2024001',
        name: 'Demo Student',
        sid: '2024001',
        className: 'Grade 9'
      }
      console.log(`Using demo student for SID: ${sid}`)
    }

    if (!student) {
      console.log(`Student not found for SID: ${sid}`)
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid Student ID or password'
        } as StudentLoginResponse,
        { status: 401 }
      )
    }

    // For now, we'll implement a simple password check
    // In production, you should hash passwords and store them securely
    // Default password could be: sid + "123" or similar pattern
    const defaultPassword = `${sid}123`
    const isValidPassword = password === defaultPassword

    if (!isValidPassword) {
      console.log(`Invalid password for SID: ${sid}`)
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid Student ID or password' 
        } as StudentLoginResponse,
        { status: 401 }
      )
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        studentId: student.id, 
        sid: student.sid,
        className: student.className 
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    console.log(`Student login successful for: ${student.name} (${sid})`)

    return NextResponse.json({
      success: true,
      student: {
        id: student.id,
        name: student.name,
        sid: student.sid,
        className: student.className
      },
      token,
      message: 'Login successful'
    } as StudentLoginResponse)

  } catch (error) {
    console.error('Error in student authentication:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      } as StudentLoginResponse,
      { status: 500 }
    )
  }
}

// Verify student token
export async function GET(request: Request) {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No token provided' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)

    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any
      
      // Verify student still exists
      let student = await prisma.student.findUnique({
        where: { id: decoded.studentId },
        select: {
          id: true,
          name: true,
          sid: true,
          className: true
        }
      })

      // Handle demo student for testing
      if (!student && decoded.studentId === 'demo-student-2024001') {
        student = {
          id: 'demo-student-2024001',
          name: 'Demo Student',
          sid: '2024001',
          className: 'Grade 9'
        }
      }

      if (!student) {
        return NextResponse.json(
          { success: false, message: 'Student not found' },
          { status: 401 }
        )
      }

      return NextResponse.json({
        success: true,
        student: {
          id: student.id,
          name: student.name,
          sid: student.sid,
          className: student.className
        }
      })

    } catch (jwtError) {
      return NextResponse.json(
        { success: false, message: 'Invalid token' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('Error verifying student token:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
