'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON>, usePara<PERSON> } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { 
  ArrowLeft, 
  Save, 
  Loader2,
  Plus,
  Trash2,
  HelpCircle
} from 'lucide-react'
import Link from 'next/link'

interface QuestionOption {
  key: string
  text: string
}

interface EditQuestionData {
  questionText: string
  questionType: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'FILL_BLANK'
  options: QuestionOption[]
  correctAnswer: string
  marks: number
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  explanation?: string
  subjectId: string
}

export default function EditQuestionPage() {
  const router = useRouter()
  const params = useParams()
  const questionId = params.id as string

  const [formData, setFormData] = useState<EditQuestionData>({
    questionText: '',
    questionType: 'MULTIPLE_CHOICE',
    options: [
      { key: 'A', text: '' },
      { key: 'B', text: '' },
      { key: 'C', text: '' },
      { key: 'D', text: '' }
    ],
    correctAnswer: '',
    marks: 1,
    difficulty: 'MEDIUM',
    explanation: '',
    subjectId: ''
  })

  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Fetch question data
  const { data: questionData, isLoading: questionLoading, error: questionError } = useQuery({
    queryKey: ['question', questionId],
    queryFn: async () => {
      const response = await fetch(`/api/questions/${questionId}`)
      if (!response.ok) throw new Error('Failed to fetch question')
      return response.json()
    }
  })

  // Fetch subjects for dropdown
  const { data: subjects } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await fetch('/api/subjects')
      if (!response.ok) throw new Error('Failed to fetch subjects')
      return response.json()
    }
  })

  // Load question data into form when fetched
  useEffect(() => {
    if (questionData?.question) {
      const question = questionData.question
      setFormData({
        questionText: question.questionText,
        questionType: question.questionType,
        options: question.options.length > 0 ? question.options : [
          { key: 'A', text: '' },
          { key: 'B', text: '' },
          { key: 'C', text: '' },
          { key: 'D', text: '' }
        ],
        correctAnswer: question.correctAnswer || '',
        marks: question.marks,
        difficulty: question.difficulty,
        explanation: question.explanation || '',
        subjectId: question.subject.id
      })
    }
  }, [questionData])

  const handleInputChange = (field: keyof EditQuestionData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleOptionChange = (index: number, text: string) => {
    const newOptions = [...formData.options]
    newOptions[index].text = text
    setFormData(prev => ({
      ...prev,
      options: newOptions
    }))
  }

  const addOption = () => {
    const nextKey = String.fromCharCode(65 + formData.options.length) // A, B, C, D, E, F...
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { key: nextKey, text: '' }]
    }))
  }

  const removeOption = (index: number) => {
    if (formData.options.length <= 2) return // Minimum 2 options
    
    const newOptions = formData.options.filter((_, i) => i !== index)
    // Re-assign keys (A, B, C, D...)
    const reKeyedOptions = newOptions.map((option, i) => ({
      ...option,
      key: String.fromCharCode(65 + i)
    }))
    
    setFormData(prev => ({
      ...prev,
      options: reKeyedOptions,
      correctAnswer: reKeyedOptions.some(opt => opt.key === prev.correctAnswer) 
        ? prev.correctAnswer 
        : ''
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear previous errors
    setError(null)
    setSuccessMessage(null)

    // Validate required fields
    if (!formData.questionText.trim()) {
      setError('Question text is required')
      return
    }

    if (!formData.subjectId) {
      setError('Please select a subject')
      return
    }

    if (!formData.marks || formData.marks < 1) {
      setError('Marks must be at least 1')
      return
    }

    // Validate based on question type
    if (formData.questionType === 'MULTIPLE_CHOICE') {
      const filledOptions = formData.options.filter(opt => opt.text.trim())
      if (filledOptions.length < 2) {
        setError('Please provide at least 2 options for multiple choice questions')
        return
      }
      if (!formData.correctAnswer) {
        setError('Please select the correct answer')
        return
      }
      // Verify correct answer exists in options
      const correctOptionExists = filledOptions.some(opt => opt.key === formData.correctAnswer)
      if (!correctOptionExists) {
        setError('The selected correct answer does not match any of the provided options')
        return
      }
    } else if (formData.questionType === 'TRUE_FALSE') {
      if (!formData.correctAnswer || !['TRUE', 'FALSE'].includes(formData.correctAnswer)) {
        setError('Please select True or False as the correct answer')
        return
      }
    } else if (formData.questionType === 'FILL_BLANK') {
      if (!formData.correctAnswer.trim()) {
        setError('Please provide the correct answer for fill-in-the-blank question')
        return
      }
    }

    try {
      setSaving(true)

      // Prepare data for submission
      const submitData = {
        questionText: formData.questionText.trim(),
        questionType: formData.questionType,
        options: formData.questionType === 'MULTIPLE_CHOICE'
          ? formData.options.filter(opt => opt.text.trim())
          : [],
        correctAnswer: formData.correctAnswer,
        marks: formData.marks,
        difficulty: formData.difficulty,
        explanation: formData.explanation?.trim() || null,
        subjectId: formData.subjectId
      }

      console.log('Submitting question update:', submitData)

      const response = await fetch(`/api/questions/${questionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData)
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        throw new Error(errorData.error || 'Failed to update question')
      }

      const result = await response.json()
      console.log('Update successful:', result)

      setSuccessMessage('Question updated successfully! Redirecting...')

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/online-exam/questions')
      }, 2000)

    } catch (error: any) {
      console.error('Error updating question:', error)
      setError(error.message || 'Failed to update question. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (questionLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    )
  }

  if (questionError || !questionData?.question) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/online-exam/questions">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Questions
              </Button>
            </Link>
          </div>
          <Alert>
            <AlertDescription>
              {questionError ? 'Failed to load question' : 'Question not found'}
            </AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/online-exam/questions">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Questions
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Edit Question
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Update question details and options
            </p>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <Alert>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {successMessage && (
          <Alert className="border-green-200 bg-green-50">
            <div className="flex items-center space-x-2">
              <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <AlertDescription className="text-green-800">{successMessage}</AlertDescription>
            </div>
          </Alert>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <HelpCircle className="h-5 w-5" />
                <span>Question Details</span>
              </CardTitle>
              <CardDescription>
                Update the question information and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Question Text */}
              <div>
                <Label htmlFor="questionText">Question Text *</Label>
                <Textarea
                  id="questionText"
                  value={formData.questionText}
                  onChange={(e) => handleInputChange('questionText', e.target.value)}
                  placeholder="Enter your question here..."
                  rows={3}
                  required
                />
              </div>

              {/* Question Type and Subject */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="questionType">Question Type *</Label>
                  <Select
                    value={formData.questionType}
                    onValueChange={(value) => handleInputChange('questionType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select question type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MULTIPLE_CHOICE">Multiple Choice</SelectItem>
                      <SelectItem value="TRUE_FALSE">True/False</SelectItem>
                      <SelectItem value="FILL_BLANK">Fill in the Blank</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="subject">Subject *</Label>
                  <Select
                    value={formData.subjectId}
                    onValueChange={(value) => handleInputChange('subjectId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects?.subjects?.map((subject: any) => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Marks and Difficulty */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="marks">Marks *</Label>
                  <Input
                    id="marks"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.marks}
                    onChange={(e) => handleInputChange('marks', parseInt(e.target.value) || 1)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="difficulty">Difficulty Level *</Label>
                  <Select
                    value={formData.difficulty}
                    onValueChange={(value) => handleInputChange('difficulty', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EASY">Easy</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HARD">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Options Section - Only for Multiple Choice */}
          {formData.questionType === 'MULTIPLE_CHOICE' && (
            <Card>
              <CardHeader>
                <CardTitle>Answer Options</CardTitle>
                <CardDescription>
                  Add answer options and select the correct one
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.options.map((option, index) => (
                  <div key={option.key} className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="correctAnswer"
                        value={option.key}
                        checked={formData.correctAnswer === option.key}
                        onChange={(e) => handleInputChange('correctAnswer', e.target.value)}
                        className="h-4 w-4 text-blue-600"
                      />
                      <Label className="font-medium">{option.key}.</Label>
                    </div>
                    <Input
                      value={option.text}
                      onChange={(e) => handleOptionChange(index, e.target.value)}
                      placeholder={`Option ${option.key}`}
                      className="flex-1"
                    />
                    {formData.options.length > 2 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeOption(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}

                {formData.options.length < 6 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addOption}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Option
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* True/False Options */}
          {formData.questionType === 'TRUE_FALSE' && (
            <Card>
              <CardHeader>
                <CardTitle>Correct Answer</CardTitle>
                <CardDescription>
                  Select the correct answer for this True/False question
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="correctAnswer"
                      value="TRUE"
                      checked={formData.correctAnswer === 'TRUE'}
                      onChange={(e) => handleInputChange('correctAnswer', e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label>True</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="correctAnswer"
                      value="FALSE"
                      checked={formData.correctAnswer === 'FALSE'}
                      onChange={(e) => handleInputChange('correctAnswer', e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label>False</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Fill in the Blank Answer */}
          {formData.questionType === 'FILL_BLANK' && (
            <Card>
              <CardHeader>
                <CardTitle>Correct Answer</CardTitle>
                <CardDescription>
                  Enter the correct answer for this fill-in-the-blank question
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Input
                  value={formData.correctAnswer}
                  onChange={(e) => handleInputChange('correctAnswer', e.target.value)}
                  placeholder="Enter the correct answer"
                />
              </CardContent>
            </Card>
          )}

          {/* Explanation */}
          <Card>
            <CardHeader>
              <CardTitle>Explanation (Optional)</CardTitle>
              <CardDescription>
                Provide an explanation for the correct answer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.explanation}
                onChange={(e) => handleInputChange('explanation', e.target.value)}
                placeholder="Explain why this is the correct answer..."
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link href="/dashboard/online-exam/questions">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Question
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}
