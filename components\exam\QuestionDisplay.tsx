'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/app/components/ui/radio-group'
import { Label } from '@/app/components/ui/label'
import { Badge } from '@/app/components/ui/badge'
import { 
  Flag, 
  FlagOff, 
  BookOpen, 
  Award,
  Clock,
  AlertCircle
} from 'lucide-react'
import { Question, QuestionType, Difficulty } from '@/app/types/exam'

interface QuestionDisplayProps {
  question: Question & { 
    order: number
    marks: number
    selectedAnswer?: string | null
    isFlagged?: boolean
    timeSpent?: number
  }
  questionNumber: number
  totalQuestions: number
  onAnswerSelect: (answer: string) => void
  onFlagToggle: () => void
  className?: string
  showQuestionInfo?: boolean
}

export default function QuestionDisplay({
  question,
  questionNumber,
  totalQuestions,
  onAnswerSelect,
  onFlagToggle,
  className = '',
  showQuestionInfo = true
}: QuestionDisplayProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<string>(question.selectedAnswer || '')
  const [questionStartTime] = useState<number>(Date.now())

  useEffect(() => {
    setSelectedAnswer(question.selectedAnswer || '')
  }, [question.selectedAnswer, question.id])

  const handleAnswerChange = (value: string) => {
    setSelectedAnswer(value)
    onAnswerSelect(value)
  }

  const getDifficultyColor = (difficulty: Difficulty) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'HARD':
        return 'bg-red-100 text-red-800 border-red-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const formatQuestionText = (text: string) => {
    // Handle basic formatting like line breaks
    return text.split('\n').map((line, index) => (
      <span key={index}>
        {line}
        {index < text.split('\n').length - 1 && <br />}
      </span>
    ))
  }

  const getTimeSpentDisplay = () => {
    if (question.timeSpent) {
      const minutes = Math.floor(question.timeSpent / 60)
      const seconds = question.timeSpent % 60
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
    return '0:00'
  }

  return (
    <Card className={`${className} shadow-sm`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <span>Question {questionNumber} of {totalQuestions}</span>
            </CardTitle>
            
            {showQuestionInfo && (
              <div className="flex items-center space-x-3 mt-2">
                <Badge variant="outline" className="flex items-center space-x-1">
                  <Award className="h-3 w-3" />
                  <span>{question.marks} {question.marks === 1 ? 'Mark' : 'Marks'}</span>
                </Badge>
                
                <Badge variant="outline" className={getDifficultyColor(question.difficulty)}>
                  {question.difficulty}
                </Badge>
                
                {question.timeSpent !== undefined && (
                  <Badge variant="outline" className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>{getTimeSpentDisplay()}</span>
                  </Badge>
                )}
              </div>
            )}
          </div>
          
          <Button
            variant={question.isFlagged ? "destructive" : "outline"}
            size="sm"
            onClick={onFlagToggle}
            className="ml-4"
          >
            {question.isFlagged ? (
              <>
                <FlagOff className="h-4 w-4 mr-1" />
                Unflag
              </>
            ) : (
              <>
                <Flag className="h-4 w-4 mr-1" />
                Flag for Review
              </>
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Question Text */}
        <div className="prose prose-sm max-w-none">
          <div className="text-gray-900 leading-relaxed text-base">
            {formatQuestionText(question.questionText)}
          </div>
        </div>

        {/* Answer Options */}
        {question.questionType === 'MULTIPLE_CHOICE' && (
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 flex items-center space-x-2">
              <AlertCircle className="h-4 w-4" />
              <span>Select the correct answer:</span>
            </h4>
            
            <RadioGroup 
              value={selectedAnswer} 
              onValueChange={handleAnswerChange}
              className="space-y-3"
            >
              {question.options.map((option) => (
                <div 
                  key={option.key} 
                  className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors ${
                    selectedAnswer === option.key 
                      ? 'bg-blue-50 border-blue-300' 
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <RadioGroupItem 
                    value={option.key} 
                    id={`option-${option.key}`}
                    className="mt-0.5"
                  />
                  <Label 
                    htmlFor={`option-${option.key}`} 
                    className="flex-1 cursor-pointer text-sm leading-relaxed"
                  >
                    <span className="font-medium mr-2">({option.key})</span>
                    {option.text}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* Question Status */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>
              Status: {selectedAnswer ? (
                <span className="text-green-600 font-medium">Answered</span>
              ) : (
                <span className="text-gray-500">Not Answered</span>
              )}
            </span>
            
            {question.isFlagged && (
              <span className="flex items-center space-x-1 text-red-600">
                <Flag className="h-3 w-3" />
                <span className="font-medium">Flagged for Review</span>
              </span>
            )}
          </div>
          
          <div className="text-sm text-gray-500">
            Question ID: {question.id.slice(-8)}
          </div>
        </div>

        {/* Answer Confirmation */}
        {selectedAnswer && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-green-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">
                Your answer: Option {selectedAnswer}
              </span>
            </div>
            <div className="text-sm text-green-700 mt-1">
              {question.options.find(opt => opt.key === selectedAnswer)?.text}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Helper component for question preview (used in navigator)
export function QuestionPreview({ 
  question, 
  questionNumber 
}: { 
  question: Question
  questionNumber: number 
}) {
  return (
    <div className="p-3 border rounded-lg bg-gray-50">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">Question {questionNumber}</span>
        <Badge variant="outline" className="text-xs">
          {question.marks} {question.marks === 1 ? 'mark' : 'marks'}
        </Badge>
      </div>
      <div className="text-sm text-gray-700 line-clamp-2">
        {question.questionText}
      </div>
    </div>
  )
}
