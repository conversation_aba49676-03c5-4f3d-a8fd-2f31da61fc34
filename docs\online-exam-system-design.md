# Online Exam System - Comprehensive Design Document

## 🎯 System Overview

### Vision
Create a robust, secure, and user-friendly online examination system that provides:
- **Students**: Intuitive exam-taking experience with modern UI/UX
- **Administrators**: Powerful exam management and analytics tools
- **Security**: Anti-cheating measures and data integrity

### Key Features
1. **Student Portal**: SID-based login, class/subject selection, timed MCQ exams
2. **Admin Dashboard**: Exam creation, question bank management, result analytics
3. **Real-time Features**: Live timer, question flagging, progress tracking
4. **Security**: Session management, browser monitoring, integrity checks

## 🗄️ Database Schema Design

### Core Models

#### 1. Exam Model
```prisma
model Exam {
  id              String    @id @default(cuid())
  title           String
  description     String?
  classId         String
  subjectId       String
  duration        Int       // Duration in minutes
  totalMarks      Int
  passingMarks    Int
  instructions    String?
  isActive        Boolean   @default(true)
  startTime       DateTime?
  endTime         DateTime?
  allowRetake     Boolean   @default(false)
  shuffleQuestions Boolean  @default(true)
  showResults     Boolean   @default(true)
  createdBy       String    // Teacher/Admin ID
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  class           Class     @relation(fields: [classId], references: [id])
  subject         Subject   @relation(fields: [subjectId], references: [id])
  questions       ExamQuestion[]
  sessions        ExamSession[]
  results         ExamResult[]

  @@map("exams")
}
```

#### 2. Question Bank Model
```prisma
model Question {
  id              String    @id @default(cuid())
  questionText    String
  questionType    QuestionType @default(MULTIPLE_CHOICE)
  options         Json      // Array of options for MCQ
  correctAnswer   String    // Correct option key (A, B, C, D)
  explanation     String?
  marks           Int       @default(1)
  difficulty      Difficulty @default(MEDIUM)
  subjectId       String
  classId         String
  tags            String[]  // For categorization
  isActive        Boolean   @default(true)
  createdBy       String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  subject         Subject   @relation(fields: [subjectId], references: [id])
  class           Class     @relation(fields: [classId], references: [id])
  examQuestions   ExamQuestion[]

  @@map("questions")
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  FILL_BLANK
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}
```

#### 3. Exam-Question Junction
```prisma
model ExamQuestion {
  id          String    @id @default(cuid())
  examId      String
  questionId  String
  order       Int       // Question order in exam
  marks       Int       // Marks for this question in this exam

  // Relations
  exam        Exam      @relation(fields: [examId], references: [id], onDelete: Cascade)
  question    Question  @relation(fields: [questionId], references: [id])

  @@unique([examId, questionId])
  @@map("exam_questions")
}
```

#### 4. Exam Session Model
```prisma
model ExamSession {
  id              String    @id @default(cuid())
  examId          String
  studentId       String
  startTime       DateTime  @default(now())
  endTime         DateTime?
  duration        Int       // Actual duration taken
  status          ExamStatus @default(IN_PROGRESS)
  ipAddress       String?
  userAgent       String?
  browserInfo     Json?     // Browser fingerprint
  isSubmitted     Boolean   @default(false)
  autoSubmitted   Boolean   @default(false)
  flaggedCount    Int       @default(0)
  warningCount    Int       @default(0)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  exam            Exam      @relation(fields: [examId], references: [id])
  student         Student   @relation(fields: [studentId], references: [id])
  responses       ExamResponse[]
  result          ExamResult?

  @@unique([examId, studentId]) // One session per student per exam
  @@map("exam_sessions")
}

enum ExamStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  EXPIRED
  TERMINATED
}
```

#### 5. Student Responses Model
```prisma
model ExamResponse {
  id              String    @id @default(cuid())
  sessionId       String
  questionId      String
  selectedAnswer  String?   // Selected option (A, B, C, D)
  isCorrect       Boolean?
  marks           Int       @default(0)
  timeSpent       Int       @default(0) // Time in seconds
  isFlagged       Boolean   @default(false)
  isVisited       Boolean   @default(false)
  responseTime    DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  session         ExamSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  question        Question    @relation(fields: [questionId], references: [id])

  @@unique([sessionId, questionId])
  @@map("exam_responses")
}
```

#### 6. Exam Results Model
```prisma
model ExamResult {
  id              String    @id @default(cuid())
  sessionId       String    @unique
  examId          String
  studentId       String
  totalQuestions  Int
  attemptedQuestions Int
  correctAnswers  Int
  wrongAnswers    Int
  marksObtained   Int
  totalMarks      Int
  percentage      Float
  grade           String?
  isPassed        Boolean
  timeTaken       Int       // Total time in minutes
  submittedAt     DateTime
  createdAt       DateTime  @default(now())

  // Relations
  session         ExamSession @relation(fields: [sessionId], references: [id])
  exam            Exam        @relation(fields: [examId], references: [id])
  student         Student     @relation(fields: [studentId], references: [id])

  @@map("exam_results")
}
```

## 🏗️ System Architecture

### Frontend Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Applications                    │
├─────────────────────────────────────────────────────────────┤
│  Student Portal (Next.js)     │  Admin Dashboard (Next.js)  │
│  - Exam Interface             │  - Exam Management          │
│  - Timer & Navigation         │  - Question Bank            │
│  - Progress Tracking          │  - Analytics                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     API Layer (Next.js)                    │
├─────────────────────────────────────────────────────────────┤
│  Authentication  │  Exam APIs  │  Question APIs  │  Results │
│  - Student Login │  - CRUD     │  - CRUD         │  - Analytics│
│  - Session Mgmt  │  - Sessions │  - Import/Export│  - Reports  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Database (PostgreSQL)                    │
│  Students │ Exams │ Questions │ Sessions │ Responses │ Results│
└─────────────────────────────────────────────────────────────┘
```

### Security Layers
1. **Authentication**: SID-based student login with secure sessions
2. **Authorization**: Role-based access control (Student/Teacher/Admin)
3. **Session Management**: Secure exam sessions with timeout handling
4. **Anti-Cheating**: Browser monitoring, tab switching detection
5. **Data Integrity**: Response validation, time tracking, audit logs

## 🎨 UI/UX Design Principles

### Student Interface Design
1. **Clean & Minimal**: Distraction-free exam environment
2. **Intuitive Navigation**: Clear question progression and status
3. **Responsive Design**: Works on desktop, tablet, and mobile
4. **Accessibility**: WCAG compliant, keyboard navigation support
5. **Real-time Feedback**: Live timer, progress indicators, save status

### Admin Interface Design
1. **Dashboard-First**: Overview of all exam activities
2. **Efficient Workflows**: Quick exam creation and question management
3. **Data Visualization**: Charts and graphs for analytics
4. **Bulk Operations**: Import/export questions, batch actions
5. **Real-time Monitoring**: Live exam sessions and student progress

## 🔧 Technical Implementation Plan

### Phase 1: Database & Core APIs
- [ ] Database schema implementation
- [ ] Authentication APIs
- [ ] Basic CRUD operations for exams and questions

### Phase 2: Admin Dashboard
- [ ] Exam management interface
- [ ] Question bank management
- [ ] Basic analytics and reporting

### Phase 3: Student Exam Interface
- [ ] Student login and exam selection
- [ ] Exam taking interface with timer
- [ ] Question navigation and flagging

### Phase 4: Advanced Features
- [ ] Real-time monitoring
- [ ] Advanced analytics
- [ ] Security enhancements
- [ ] Performance optimizations

### Phase 5: Testing & Deployment
- [ ] Comprehensive testing
- [ ] Performance testing
- [ ] Security auditing
- [ ] Production deployment

## 📊 Key Metrics & Analytics

### For Administrators
- Exam completion rates
- Average scores by class/subject
- Question difficulty analysis
- Time spent per question
- Student performance trends

### For Teachers
- Individual student performance
- Class-wise comparisons
- Question effectiveness
- Common wrong answers
- Improvement suggestions

## 🔒 Security Considerations

### Anti-Cheating Measures
1. **Browser Restrictions**: Fullscreen mode, disable right-click
2. **Tab Monitoring**: Detect tab switches and warn students
3. **Time Tracking**: Monitor time spent on each question
4. **Session Validation**: Continuous session validation
5. **IP Monitoring**: Track IP changes during exam
6. **Randomization**: Shuffle questions and options

### Data Protection
1. **Encryption**: All sensitive data encrypted at rest and in transit
2. **Audit Logs**: Complete audit trail of all actions
3. **Backup Strategy**: Regular automated backups
4. **Access Control**: Strict role-based permissions
5. **GDPR Compliance**: Data privacy and right to deletion

This comprehensive design ensures a robust, secure, and user-friendly online examination system that meets modern educational standards while providing excellent user experience for both students and administrators.
