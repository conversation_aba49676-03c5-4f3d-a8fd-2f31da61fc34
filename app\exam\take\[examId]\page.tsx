'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useRouter, usePara<PERSON>, useSearchParams } from 'next/navigation'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Button } from '@/app/components/ui/button'
import { Loader2, <PERSON><PERSON><PERSON><PERSON>gle, ArrowLeft, Maximize, Minimize } from 'lucide-react'
import ExamTimer from '@/components/exam/ExamTimer'
import QuestionNavigator from '@/components/exam/QuestionNavigator'
import QuestionDisplay from '@/components/exam/QuestionDisplay'
import ExamControls from '@/components/exam/ExamControls'
import ExamSecurityMonitor from '@/components/exam/ExamSecurityMonitor'
import { useExamAutoSave, useQuestionTimer } from '@/hooks/useExamAutoSave'
import { ExamUIState, Question, QuestionNavigatorItem } from '@/app/types/exam'

interface ExamQuestion extends Question {
  order: number
  marks: number
  selectedAnswer?: string | null
  isFlagged?: boolean
  timeSpent?: number
}

export default function ExamTakePage() {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()
  const examId = params.examId as string
  const sessionId = searchParams.get('sessionId')

  // State management
  const [examState, setExamState] = useState<ExamUIState>({
    currentQuestionIndex: 0,
    responses: {},
    flaggedQuestions: new Set(),
    visitedQuestions: new Set([0]),
    timeRemaining: 0,
    isSubmitting: false,
    showNavigator: false,
    warningCount: 0
  })

  const [questions, setQuestions] = useState<ExamQuestion[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [examInfo, setExamInfo] = useState<any>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState<number>(Date.now())
  const [securityViolations, setSecurityViolations] = useState<number>(0)
  const [isLargeScreen, setIsLargeScreen] = useState(false)

  // Refs for tracking
  const questionStartTime = useRef<number>(Date.now())
  const autoSaveInterval = useRef<NodeJS.Timeout>()
  const visibilityChangeCount = useRef<number>(0)

  // Auto-save hook
  const { queueSave, forceSave, getSaveStatus } = useExamAutoSave({
    examId,
    sessionId: sessionId || '',
    onSave: (success) => {
      if (success) {
        setLastSaveTime(Date.now())
      }
    },
    onError: (error) => {
      console.error('Auto-save error:', error)
    }
  })

  // Question timer hook
  const { resetTimer, getTimeSpent } = useQuestionTimer()

  // Check authentication and load exam
  useEffect(() => {
    const token = localStorage.getItem('examToken')
    if (!token) {
      router.push('/exam/login')
      return
    }

    if (!sessionId) {
      setError('Invalid session. Please start the exam again.')
      return
    }

    loadExamQuestions(token)
  }, [examId, sessionId, router])

  // Handle window resize for responsive navigation
  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1024)
    }

    // Initial check
    checkScreenSize()

    // Add event listener
    window.addEventListener('resize', checkScreenSize)

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Auto-save functionality
  useEffect(() => {
    autoSaveInterval.current = setInterval(() => {
      if (!isSaving && questions.length > 0) {
        saveCurrentResponse()
      }
    }, 30000) // Auto-save every 30 seconds

    return () => {
      if (autoSaveInterval.current) {
        clearInterval(autoSaveInterval.current)
      }
    }
  }, [questions, examState.currentQuestionIndex, isSaving])

  // Visibility change detection (anti-cheating)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        visibilityChangeCount.current += 1
        if (visibilityChangeCount.current >= 3) {
          // Too many tab switches, could implement warning or auto-submit
          console.warn('Multiple tab switches detected')
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [])

  // Fullscreen management
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // Prevent right-click and keyboard shortcuts (anti-cheating)
  useEffect(() => {
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault()
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent F12, Ctrl+Shift+I, Ctrl+U, etc.
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.key === 'u')
      ) {
        e.preventDefault()
      }
    }

    document.addEventListener('contextmenu', handleContextMenu)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  const loadExamQuestions = async (token: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/exam/${examId}/questions?sessionId=${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setQuestions(data.questions)
        setExamState(prev => ({
          ...prev,
          timeRemaining: data.timeRemaining,
          warningCount: data.sessionInfo.warningCount
        }))
        setExamInfo(data.sessionInfo)

        // Initialize responses from existing data
        const initialResponses: Record<string, string> = {}
        const initialFlagged = new Set<string>()
        const initialVisited = new Set<number>([0])

        data.questions.forEach((q: ExamQuestion, index: number) => {
          if (q.selectedAnswer) {
            initialResponses[q.id] = q.selectedAnswer
            initialVisited.add(index)
          }
          if (q.isFlagged) {
            initialFlagged.add(q.id)
          }
        })

        setExamState(prev => ({
          ...prev,
          responses: initialResponses,
          flaggedQuestions: initialFlagged,
          visitedQuestions: initialVisited
        }))

      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to load exam questions')
      }
    } catch (error) {
      console.error('Error loading exam:', error)
      setError('Network error. Please check your connection.')
    } finally {
      setIsLoading(false)
    }
  }

  const saveCurrentResponse = useCallback(async () => {
    if (!questions.length || isSaving) return

    const currentQuestion = questions[examState.currentQuestionIndex]
    const selectedAnswer = examState.responses[currentQuestion.id]
    const isFlagged = examState.flaggedQuestions.has(currentQuestion.id)
    const timeSpent = Math.floor((Date.now() - questionStartTime.current) / 1000)

    try {
      setIsSaving(true)
      const token = localStorage.getItem('examToken')
      
      const response = await fetch(`/api/exam/${examId}/response`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          sessionId,
          questionId: currentQuestion.id,
          selectedAnswer,
          isFlagged,
          timeSpent
        })
      })

      if (response.ok) {
        const data = await response.json()
        setExamState(prev => ({ ...prev, timeRemaining: data.timeRemaining }))
        setLastSaveTime(Date.now())
      }
    } catch (error) {
      console.error('Error saving response:', error)
    } finally {
      setIsSaving(false)
    }
  }, [examId, sessionId, questions, examState, isSaving])

  const handleAnswerSelect = (answer: string) => {
    const currentQuestion = questions[examState.currentQuestionIndex]
    setExamState(prev => ({
      ...prev,
      responses: {
        ...prev.responses,
        [currentQuestion.id]: answer
      }
    }))

    // Queue auto-save
    queueSave({
      questionId: currentQuestion.id,
      selectedAnswer: answer,
      isFlagged: examState.flaggedQuestions.has(currentQuestion.id),
      timeSpent: getTimeSpent()
    })
  }

  const handleFlagToggle = () => {
    const currentQuestion = questions[examState.currentQuestionIndex]
    setExamState(prev => {
      const newFlagged = new Set(prev.flaggedQuestions)
      if (newFlagged.has(currentQuestion.id)) {
        newFlagged.delete(currentQuestion.id)
      } else {
        newFlagged.add(currentQuestion.id)
      }
      return {
        ...prev,
        flaggedQuestions: newFlagged
      }
    })
  }

  const handleQuestionNavigation = (index: number) => {
    if (index >= 0 && index < questions.length) {
      // Save current response before navigating
      saveCurrentResponse()

      setExamState(prev => ({
        ...prev,
        currentQuestionIndex: index,
        visitedQuestions: new Set([...prev.visitedQuestions, index])
      }))
      questionStartTime.current = Date.now()
      resetTimer() // Reset question timer
    }
  }

  const handlePrevious = () => {
    if (examState.currentQuestionIndex > 0) {
      handleQuestionNavigation(examState.currentQuestionIndex - 1)
    }
  }

  const handleNext = () => {
    if (examState.currentQuestionIndex < questions.length - 1) {
      handleQuestionNavigation(examState.currentQuestionIndex + 1)
    }
  }

  const handleSubmitExam = async () => {
    try {
      setExamState(prev => ({ ...prev, isSubmitting: true }))

      // Force save all pending responses
      await forceSave()

      const token = localStorage.getItem('examToken')
      const response = await fetch('/api/exam/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ sessionId })
      })

      if (response.ok) {
        const data = await response.json()
        // Redirect to results page
        router.push(`/exam/result/${data.resultId}`)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to submit exam')
      }
    } catch (error) {
      console.error('Error submitting exam:', error)
      setError('Network error. Please try again.')
    } finally {
      setExamState(prev => ({ ...prev, isSubmitting: false }))
    }
  }

  const handleSecurityViolation = (violationType: string, count: number) => {
    setSecurityViolations(count)
    console.warn(`Security violation: ${violationType}, total: ${count}`)
  }

  const handleForceSubmit = () => {
    alert('Too many security violations detected. Your exam will be submitted automatically.')
    handleSubmitExam()
  }

  const handleTimeUp = () => {
    // Auto-submit when time is up
    handleSubmitExam()
  }

  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen()
      } else {
        await document.exitFullscreen()
      }
    } catch (error) {
      console.error('Fullscreen error:', error)
    }
  }

  const handleGoBack = () => {
    if (confirm('Are you sure you want to leave the exam? Your progress will be saved.')) {
      saveCurrentResponse()
      router.push('/exam/select')
    }
  }

  // Prepare navigator items
  const navigatorItems: QuestionNavigatorItem[] = questions.map((question, index) => {
    const isAnswered = !!examState.responses[question.id]
    const isFlagged = examState.flaggedQuestions.has(question.id)
    const isVisited = examState.visitedQuestions.has(index)
    const isCurrent = index === examState.currentQuestionIndex

    let status: 'answered' | 'flagged' | 'visited' | 'not-visited'
    if (isAnswered) status = 'answered'
    else if (isFlagged) status = 'flagged'
    else if (isVisited) status = 'visited'
    else status = 'not-visited'

    return {
      questionId: question.id,
      order: question.order,
      status,
      isCurrent
    }
  })

  const answeredCount = Object.keys(examState.responses).length
  const flaggedCount = examState.flaggedQuestions.size

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading exam...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <Button onClick={handleGoBack} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Exams
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!questions.length) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No questions found for this exam.</p>
          <Button onClick={handleGoBack} variant="outline" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exams
          </Button>
        </div>
      </div>
    )
  }

  const currentQuestion = questions[examState.currentQuestionIndex]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Exit Exam
            </Button>
            <div className="text-sm text-gray-600">
              Session: {sessionId?.slice(-8)}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={toggleFullscreen}>
              {isFullscreen ? (
                <>
                  <Minimize className="h-4 w-4 mr-2" />
                  Exit Fullscreen
                </>
              ) : (
                <>
                  <Maximize className="h-4 w-4 mr-2" />
                  Fullscreen
                </>
              )}
            </Button>
            
            <div className="text-sm text-gray-600">
              Last saved: {new Date(lastSaveTime).toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-4">
        {/* Security Monitor */}
        <ExamSecurityMonitor
          onSecurityViolation={handleSecurityViolation}
          onForceSubmit={handleForceSubmit}
          maxViolationsBeforeSubmit={5}
          showSecurityStatus={false}
          className="mb-6"
        />

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Timer and Navigator */}
          <div className="lg:col-span-1 space-y-6">
            <ExamTimer
              initialTimeRemaining={examState.timeRemaining}
              onTimeUp={handleTimeUp}
              onWarning={(timeRemaining) => {
                setExamState(prev => ({ ...prev, warningCount: prev.warningCount + 1 }))
              }}
            />

            <QuestionNavigator
              questions={navigatorItems}
              currentQuestionIndex={examState.currentQuestionIndex}
              onQuestionSelect={handleQuestionNavigation}
              onClose={() => setExamState(prev => ({ ...prev, showNavigator: false }))}
              isVisible={examState.showNavigator || isLargeScreen}
            />
          </div>

          {/* Main Content - Question Display */}
          <div className="lg:col-span-3">
            <QuestionDisplay
              question={{
                ...currentQuestion,
                selectedAnswer: examState.responses[currentQuestion.id],
                isFlagged: examState.flaggedQuestions.has(currentQuestion.id)
              }}
              questionNumber={examState.currentQuestionIndex + 1}
              totalQuestions={questions.length}
              onAnswerSelect={handleAnswerSelect}
              onFlagToggle={handleFlagToggle}
            />
          </div>
        </div>
      </div>

      {/* Bottom Controls */}
      <ExamControls
        currentQuestionIndex={examState.currentQuestionIndex}
        totalQuestions={questions.length}
        answeredCount={answeredCount}
        flaggedCount={flaggedCount}
        timeRemaining={examState.timeRemaining}
        isSubmitting={examState.isSubmitting}
        isSaving={isSaving}
        onPrevious={handlePrevious}
        onNext={handleNext}
        onSubmit={handleSubmitExam}
        onSave={saveCurrentResponse}
        onToggleNavigator={() => setExamState(prev => ({ ...prev, showNavigator: !prev.showNavigator }))}
        showNavigator={examState.showNavigator}
        className="fixed bottom-0 left-0 right-0 z-10"
      />

      {/* Bottom padding to account for fixed controls */}
      <div className="h-24" />
    </div>
  )
}
