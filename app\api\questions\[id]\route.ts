import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET - Fetch individual question details
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to view questions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { id } = params

    // Fetch question with related data
    const question = await prisma.question.findUnique({
      where: { id },
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            examQuestions: true
          }
        }
      }
    })

    if (!question) {
      return NextResponse.json(
        { error: 'Question not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      question: {
        id: question.id,
        questionText: question.questionText,
        questionType: question.questionType,
        options: question.options,
        correctAnswer: question.correctAnswer,
        explanation: question.explanation,
        marks: question.marks,
        difficulty: question.difficulty,
        subject: question.subject,
        class: question.class,
        usageCount: question._count.examQuestions,
        isActive: question.isActive,
        createdAt: question.createdAt,
        updatedAt: question.updatedAt
      }
    })

  } catch (error) {
    console.error('Error fetching question:', error)
    return NextResponse.json(
      { error: 'Failed to fetch question' },
      { status: 500 }
    )
  }
}

// PUT - Update question
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to edit questions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { id } = params
    const body = await request.json()

    // Check if question exists
    const existingQuestion = await prisma.question.findUnique({
      where: { id }
    })

    if (!existingQuestion) {
      return NextResponse.json(
        { error: 'Question not found' },
        { status: 404 }
      )
    }

    // Validate required fields
    const {
      questionText,
      questionType,
      options,
      correctAnswer,
      marks,
      difficulty,
      explanation,
      subjectId
    } = body

    if (!questionText || !questionType || !subjectId || !marks) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify subject exists and get classId
    const subject = await prisma.subject.findUnique({
      where: { id: subjectId },
      select: {
        id: true,
        name: true,
        classId: true
      }
    })

    if (!subject) {
      return NextResponse.json(
        { error: 'Subject not found' },
        { status: 404 }
      )
    }

    // Update the question
    const updatedQuestion = await prisma.question.update({
      where: { id },
      data: {
        questionText: questionText.trim(),
        questionType,
        options: options || [],
        correctAnswer: correctAnswer || null,
        marks,
        difficulty,
        explanation: explanation?.trim() || null,
        subjectId,
        classId: subject.classId,
        updatedAt: new Date()
      },
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    console.log(`Question updated: ${updatedQuestion.id} by ${userData.email}`)

    return NextResponse.json({
      success: true,
      question: {
        id: updatedQuestion.id,
        questionText: updatedQuestion.questionText,
        questionType: updatedQuestion.questionType,
        options: updatedQuestion.options,
        marks: updatedQuestion.marks,
        difficulty: updatedQuestion.difficulty,
        subject: updatedQuestion.subject,
        class: updatedQuestion.class,
        updatedAt: updatedQuestion.updatedAt
      },
      message: 'Question updated successfully'
    })

  } catch (error) {
    console.error('Error updating question:', error)
    return NextResponse.json(
      { error: 'Failed to update question' },
      { status: 500 }
    )
  }
}

// DELETE - Delete question
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to delete questions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions. Only Super Admin, Admin, and Supervisor can delete questions.' },
        { status: 403 }
      )
    }

    const { id } = params

    // Check if question exists and get usage count
    const question = await prisma.question.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            examQuestions: true
          }
        }
      }
    })

    if (!question) {
      return NextResponse.json(
        { error: 'Question not found' },
        { status: 404 }
      )
    }

    // Check if question is being used in any exams
    if (question._count.examQuestions > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete question. It is currently being used in ${question._count.examQuestions} exam(s). Please remove it from all exams first.` 
        },
        { status: 400 }
      )
    }

    // Delete the question
    await prisma.question.delete({
      where: { id }
    })

    console.log(`Question deleted: ${id} by ${userData.email}`)

    return NextResponse.json({
      success: true,
      message: 'Question deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting question:', error)
    return NextResponse.json(
      { error: 'Failed to delete question' },
      { status: 500 }
    )
  }
}
