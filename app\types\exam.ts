// Online Exam System Type Definitions

export interface Exam {
  id: string
  title: string
  description?: string
  classId: string
  subjectId: string
  duration: number // Duration in minutes
  totalMarks: number
  passingMarks: number
  instructions?: string
  isActive: boolean
  startTime?: Date
  endTime?: Date
  allowRetake: boolean
  shuffleQuestions: boolean
  showResults: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
  
  // Relations
  class?: {
    id: string
    name: string
  }
  subject?: {
    id: string
    name: string
  }
  questions?: ExamQuestion[]
  sessions?: ExamSession[]
  results?: ExamResult[]
}

export interface Question {
  id: string
  questionText: string
  questionType: QuestionType
  options: QuestionOption[]
  correctAnswer: string // Correct option key (A, B, C, D)
  explanation?: string
  marks: number
  difficulty: Difficulty
  subjectId: string
  classId: string
  tags: string[]
  isActive: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
  
  // Relations
  subject?: {
    id: string
    name: string
  }
  class?: {
    id: string
    name: string
  }
  examQuestions?: ExamQuestion[]
  responses?: ExamResponse[]
}

export interface QuestionOption {
  key: string // A, B, C, D
  text: string
  isCorrect?: boolean
}

export interface ExamQuestion {
  id: string
  examId: string
  questionId: string
  order: number
  marks: number
  
  // Relations
  exam?: Exam
  question?: Question
}

export interface ExamSession {
  id: string
  examId: string
  studentId: string
  startTime: Date
  endTime?: Date
  duration?: number // Actual duration taken in minutes
  status: ExamStatus
  ipAddress?: string
  userAgent?: string
  browserInfo?: any
  isSubmitted: boolean
  autoSubmitted: boolean
  flaggedCount: number
  warningCount: number
  createdAt: Date
  updatedAt: Date
  
  // Relations
  exam?: Exam
  student?: {
    id: string
    name: string
    sid: string
    className: string
  }
  responses?: ExamResponse[]
  result?: ExamResult
}

export interface ExamResponse {
  id: string
  sessionId: string
  questionId: string
  selectedAnswer?: string // Selected option (A, B, C, D)
  isCorrect?: boolean
  marks: number
  timeSpent: number // Time in seconds
  isFlagged: boolean
  isVisited: boolean
  responseTime: Date
  updatedAt: Date
  
  // Relations
  session?: ExamSession
  question?: Question
}

export interface ExamResult {
  id: string
  sessionId: string
  examId: string
  studentId: string
  totalQuestions: number
  attemptedQuestions: number
  correctAnswers: number
  wrongAnswers: number
  marksObtained: number
  totalMarks: number
  percentage: number
  grade?: string
  isPassed: boolean
  timeTaken: number // Total time in minutes
  submittedAt: Date
  createdAt: Date
  
  // Relations
  session?: ExamSession
  exam?: Exam
  student?: {
    id: string
    name: string
    sid: string
    className: string
  }
}

// Enums
export enum QuestionType {
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  TRUE_FALSE = 'TRUE_FALSE',
  FILL_BLANK = 'FILL_BLANK'
}

export enum Difficulty {
  EASY = 'EASY',
  MEDIUM = 'MEDIUM',
  HARD = 'HARD'
}

export enum ExamStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED'
}

// API Request/Response Types
export interface CreateExamRequest {
  title: string
  description?: string
  classId: string
  subjectId: string
  duration: number
  totalMarks: number
  passingMarks: number
  instructions?: string
  startTime?: Date
  endTime?: Date
  allowRetake?: boolean
  shuffleQuestions?: boolean
  showResults?: boolean
  questionIds: string[]
}

export interface CreateQuestionRequest {
  questionText: string
  questionType: QuestionType
  options: QuestionOption[]
  correctAnswer: string
  explanation?: string
  marks?: number
  difficulty?: Difficulty
  subjectId: string
  classId: string
  tags?: string[]
}

export interface StartExamRequest {
  examId: string
  studentId: string
}

export interface SubmitResponseRequest {
  sessionId: string
  questionId: string
  selectedAnswer?: string
  isFlagged?: boolean
  timeSpent?: number
}

export interface SubmitExamRequest {
  sessionId: string
}

export interface StudentLoginRequest {
  sid: string
  password: string
}

export interface StudentLoginResponse {
  success: boolean
  student?: {
    id: string
    name: string
    sid: string
    className: string
  }
  token?: string
  message?: string
}

export interface AvailableExamsResponse {
  exams: Exam[]
}

export interface ExamQuestionsResponse {
  questions: (Question & { order: number; marks: number })[]
  shuffled: boolean
}

export interface ExamSessionResponse {
  session: ExamSession
  timeRemaining: number // in seconds
}

// UI State Types
export interface ExamUIState {
  currentQuestionIndex: number
  responses: Record<string, string> // questionId -> selectedAnswer
  flaggedQuestions: Set<string>
  visitedQuestions: Set<string>
  timeRemaining: number // in seconds
  isSubmitting: boolean
  showNavigator: boolean
  warningCount: number
}

export interface QuestionNavigatorItem {
  questionId: string
  order: number
  status: 'answered' | 'flagged' | 'visited' | 'not-visited'
  isCurrent: boolean
}

// Analytics Types
export interface ExamAnalytics {
  totalExams: number
  activeExams: number
  totalStudents: number
  averageScore: number
  completionRate: number
  topPerformers: {
    studentName: string
    score: number
    percentage: number
  }[]
  subjectWisePerformance: {
    subjectName: string
    averageScore: number
    totalStudents: number
  }[]
  difficultyAnalysis: {
    difficulty: Difficulty
    averageScore: number
    questionCount: number
  }[]
}

export interface QuestionAnalytics {
  questionId: string
  questionText: string
  difficulty: Difficulty
  totalAttempts: number
  correctAttempts: number
  successRate: number
  averageTimeSpent: number
  commonWrongAnswers: {
    option: string
    count: number
  }[]
}
