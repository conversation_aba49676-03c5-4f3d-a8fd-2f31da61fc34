'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Badge } from '@/app/components/ui/badge'
import {
  Plus,
  Search,
  Filter,
  HelpCircle,
  BookOpen,
  Eye,
  Edit,
  Trash2,
  Loader2
} from 'lucide-react'
import Link from 'next/link'

interface Question {
  id: string
  questionText: string
  questionType: string
  marks: number
  difficulty: string
  subject: { name: string }
  usageCount: number
  createdAt: string
}

export default function QuestionsPage() {
  const [search, setSearch] = useState('')
  const [filters, setFilters] = useState({
    subjectId: 'all',
    difficulty: 'all',
    questionType: 'all'
  })
  const [deletingQuestionId, setDeletingQuestionId] = useState<string | null>(null)

  const queryClient = useQueryClient()

  // Delete question mutation
  const deleteQuestionMutation = useMutation({
    mutationFn: async (questionId: string) => {
      const response = await fetch(`/api/questions/${questionId}`, {
        method: 'DELETE',
        credentials: 'include'
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete question')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      setDeletingQuestionId(null)
    },
    onError: (error: any) => {
      console.error('Error deleting question:', error)
      alert(error.message || 'Failed to delete question')
      setDeletingQuestionId(null)
    }
  })

  // Handle delete question
  const handleDeleteQuestion = async (question: Question) => {
    if (question.usageCount > 0) {
      alert(`Cannot delete this question. It is currently being used in ${question.usageCount} exam(s). Please remove it from all exams first.`)
      return
    }

    if (!confirm(`Are you sure you want to delete this question? This action cannot be undone.\n\nQuestion: "${question.questionText.substring(0, 100)}..."`)) {
      return
    }

    setDeletingQuestionId(question.id)
    deleteQuestionMutation.mutate(question.id)
  }

  // Fetch subjects for filter
  const { data: subjects } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await fetch('/api/subjects')
      if (!response.ok) throw new Error('Failed to fetch subjects')
      return response.json()
    }
  })

  // Fetch questions
  const { data: questionsData, isLoading, refetch } = useQuery({
    queryKey: ['questions', search, filters],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (search) params.append('search', search)
      if (filters.subjectId && filters.subjectId !== 'all') params.append('subjectId', filters.subjectId)
      if (filters.difficulty && filters.difficulty !== 'all') params.append('difficulty', filters.difficulty)
      if (filters.questionType && filters.questionType !== 'all') params.append('questionType', filters.questionType)
      params.append('limit', '20')

      const response = await fetch(`/api/questions?${params}`)
      if (!response.ok) throw new Error('Failed to fetch questions')
      return response.json()
    }
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800 border-green-300'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'HARD': return 'bg-red-100 text-red-800 border-red-300'
      default: return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'MULTIPLE_CHOICE': return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'TRUE_FALSE': return 'bg-purple-100 text-purple-800 border-purple-300'
      case 'SHORT_ANSWER': return 'bg-orange-100 text-orange-800 border-orange-300'
      default: return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const formatQuestionType = (type: string) => {
    return type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const truncateText = (text: string, maxLength: number = 100) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Question Bank
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your exam questions
            </p>
          </div>
          
          <Link href="/dashboard/online-exam/questions/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Question
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Input
                  placeholder="Search questions..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full"
                />
              </div>
              
              <Select 
                value={filters.subjectId} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, subjectId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects?.map((subject: any) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select 
                value={filters.difficulty} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, difficulty: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Difficulties" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Difficulties</SelectItem>
                  <SelectItem value="EASY">Easy</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HARD">Hard</SelectItem>
                </SelectContent>
              </Select>

              <Select 
                value={filters.questionType} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, questionType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="MULTIPLE_CHOICE">Multiple Choice</SelectItem>
                  <SelectItem value="TRUE_FALSE">True/False</SelectItem>
                  <SelectItem value="SHORT_ANSWER">Short Answer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Questions List */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading questions...</p>
            </div>
          ) : questionsData?.questions?.length > 0 ? (
            <>
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Found {questionsData.questions.length} question{questionsData.questions.length !== 1 ? 's' : ''}
                </p>
              </div>
              
              <div className="grid gap-4">
                {questionsData.questions.map((question: Question) => (
                  <Card key={question.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-start space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg">
                              <HelpCircle className="h-5 w-5 text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 mb-2">
                                {truncateText(question.questionText, 150)}
                              </p>
                              
                              <div className="flex items-center space-x-3 mb-3">
                                <Badge variant="outline" className="flex items-center space-x-1">
                                  <BookOpen className="h-3 w-3" />
                                  <span>{question.subject.name}</span>
                                </Badge>
                                
                                <Badge className={getDifficultyColor(question.difficulty)}>
                                  {question.difficulty}
                                </Badge>
                                
                                <Badge className={getTypeColor(question.questionType)}>
                                  {formatQuestionType(question.questionType)}
                                </Badge>
                                
                                <Badge variant="outline">
                                  {question.marks} mark{question.marks !== 1 ? 's' : ''}
                                </Badge>
                              </div>
                              
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <span>Used in {question.usageCount} exam{question.usageCount !== 1 ? 's' : ''}</span>
                                <span>•</span>
                                <span>Created {new Date(question.createdAt).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <Link href={`/dashboard/online-exam/questions/${question.id}`}>
                            <Button variant="outline" size="sm" title="View Question">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/dashboard/online-exam/questions/${question.id}/edit`}>
                            <Button variant="outline" size="sm" title="Edit Question">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleDeleteQuestion(question)}
                            disabled={deletingQuestionId === question.id}
                            title="Delete Question"
                          >
                            {deletingQuestionId === question.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              {/* Pagination would go here */}
              {questionsData.pagination && questionsData.pagination.totalPages > 1 && (
                <div className="flex justify-center mt-6">
                  <p className="text-sm text-gray-600">
                    Page {questionsData.pagination.page} of {questionsData.pagination.totalPages}
                  </p>
                </div>
              )}
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
                <p className="text-gray-600 mb-4">
                  {search || Object.values(filters).some(f => f) 
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first question'
                  }
                </p>
                <Link href="/dashboard/online-exam/questions/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Question
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
