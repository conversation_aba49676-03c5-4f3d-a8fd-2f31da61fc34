'use client'

import { useState } from 'react'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import {
  ChevronLeft,
  ChevronRight,
  Send,
  Save,
  LayoutGrid,
  AlertTriangle,
  CheckCircle,
  Clock,
  Flag
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/components/ui/dialog'
import { Alert, AlertDescription } from '@/app/components/ui/alert'

interface ExamControlsProps {
  currentQuestionIndex: number
  totalQuestions: number
  answeredCount: number
  flaggedCount: number
  timeRemaining: number
  isSubmitting: boolean
  isSaving: boolean
  onPrevious: () => void
  onNext: () => void
  onSubmit: () => void
  onSave: () => void
  onToggleNavigator: () => void
  showNavigator: boolean
  className?: string
}

export default function ExamControls({
  currentQuestionIndex,
  totalQuestions,
  answeredCount,
  flaggedCount,
  timeRemaining,
  isSubmitting,
  isSaving,
  onPrevious,
  onNext,
  onSubmit,
  onSave,
  onToggleNavigator,
  showNavigator,
  className = ''
}: ExamControlsProps) {
  const [showSubmitDialog, setShowSubmitDialog] = useState(false)

  // Safety checks to prevent errors during initial render
  const safeCurrentQuestionIndex = currentQuestionIndex || 0
  const safeTotalQuestions = totalQuestions || 0
  const safeAnsweredCount = answeredCount || 0
  const safeFlaggedCount = flaggedCount || 0
  const safeTimeRemaining = timeRemaining || 0

  const unansweredCount = safeTotalQuestions - safeAnsweredCount
  const isLastQuestion = safeCurrentQuestionIndex === safeTotalQuestions - 1
  const isFirstQuestion = safeCurrentQuestionIndex === 0

  // Don't render if essential data is missing
  if (safeTotalQuestions === 0) {
    return null
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleSubmitClick = () => {
    if (unansweredCount > 0) {
      setShowSubmitDialog(true)
    } else {
      onSubmit()
    }
  }

  const handleConfirmSubmit = () => {
    setShowSubmitDialog(false)
    onSubmit()
  }

  return (
    <div className={`bg-white border-t border-gray-200 p-4 ${className}`}>
      <div className="max-w-7xl mx-auto">
        {/* Top Row - Progress and Stats */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            {/* Progress */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">
                Question {safeCurrentQuestionIndex + 1} of {safeTotalQuestions}
              </span>
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((safeCurrentQuestionIndex + 1) / safeTotalQuestions) * 100}%` }}
                />
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">
                <CheckCircle className="h-3 w-3 mr-1" />
                Answered: {safeAnsweredCount}
              </Badge>

              {safeFlaggedCount > 0 && (
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">
                  <Flag className="h-3 w-3 mr-1" />
                  Flagged: {safeFlaggedCount}
                </Badge>
              )}

              {unansweredCount > 0 && (
                <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-300">
                  Remaining: {unansweredCount}
                </Badge>
              )}
            </div>
          </div>

          {/* Time Display */}
          <div className="flex items-center space-x-2">
            <Clock className={`h-4 w-4 ${safeTimeRemaining <= 300 ? 'text-red-600' : 'text-gray-600'}`} />
            <span className={`font-mono text-sm font-medium ${
              safeTimeRemaining <= 300 ? 'text-red-600' : 'text-gray-700'
            }`}>
              {formatTime(safeTimeRemaining)}
            </span>
          </div>
        </div>

        {/* Bottom Row - Navigation and Actions */}
        <div className="flex items-center justify-between">
          {/* Left - Navigation */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={onPrevious}
              disabled={isFirstQuestion}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <Button
              variant="outline"
              onClick={onNext}
              disabled={isLastQuestion}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              onClick={onToggleNavigator}
              className={`flex items-center space-x-2 ${showNavigator ? 'bg-blue-50 border-blue-300' : ''}`}
            >
              <LayoutGrid className="h-4 w-4" />
              <span>Navigator</span>
            </Button>
          </div>

          {/* Right - Actions */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={onSave}
              disabled={isSaving}
              className="flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{isSaving ? 'Saving...' : 'Save Progress'}</span>
            </Button>

            <Dialog open={showSubmitDialog} onOpenChange={setShowSubmitDialog}>
              <DialogTrigger asChild>
                <Button
                  onClick={handleSubmitClick}
                  disabled={isSubmitting}
                  className="bg-green-600 hover:bg-green-700 text-white flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>{isSubmitting ? 'Submitting...' : 'Submit Exam'}</span>
                </Button>
              </DialogTrigger>
              
              <DialogContent>
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    <span>Confirm Exam Submission</span>
                  </DialogTitle>
                  <DialogDescription>
                    Are you sure you want to submit your exam? This action cannot be undone.
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  {/* Submission Summary */}
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <h4 className="font-medium text-gray-900">Submission Summary:</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Total Questions:</span>
                        <span className="ml-2 font-medium">{safeTotalQuestions}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Answered:</span>
                        <span className="ml-2 font-medium text-green-600">{safeAnsweredCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Unanswered:</span>
                        <span className="ml-2 font-medium text-red-600">{unansweredCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Flagged:</span>
                        <span className="ml-2 font-medium text-orange-600">{safeFlaggedCount}</span>
                      </div>
                    </div>
                  </div>

                  {/* Warnings */}
                  {unansweredCount > 0 && (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        You have {unansweredCount} unanswered question{unansweredCount > 1 ? 's' : ''}. 
                        These will be marked as incorrect.
                      </AlertDescription>
                    </Alert>
                  )}

                  {flaggedCount > 0 && (
                    <Alert>
                      <Flag className="h-4 w-4" />
                      <AlertDescription>
                        You have {flaggedCount} question{flaggedCount > 1 ? 's' : ''} flagged for review. 
                        Make sure you've reviewed them before submitting.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowSubmitDialog(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleConfirmSubmit}
                    disabled={isSubmitting}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Exam'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  )
}
