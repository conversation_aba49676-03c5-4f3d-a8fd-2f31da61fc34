'use client'

import { useEffect, useRef, useCallback } from 'react'

interface AutoSaveConfig {
  examId: string
  sessionId: string
  interval?: number // in milliseconds, default 30000 (30 seconds)
  onSave?: (success: boolean, error?: string) => void
  onError?: (error: string) => void
}

interface SaveData {
  questionId: string
  selectedAnswer?: string | null
  isFlagged?: boolean
  timeSpent?: number
}

export function useExamAutoSave(config: AutoSaveConfig) {
  const {
    examId,
    sessionId,
    interval = 30000, // 30 seconds
    onSave,
    onError
  } = config

  const saveQueue = useRef<Map<string, SaveData>>(new Map())
  const isSaving = useRef(false)
  const lastSaveTime = useRef<number>(Date.now())
  const autoSaveInterval = useRef<NodeJS.Timeout>()

  const saveResponse = useCallback(async (data: SaveData): Promise<boolean> => {
    try {
      const token = localStorage.getItem('examToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/exam/${examId}/response`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          sessionId,
          ...data
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save response')
      }

      return true
    } catch (error) {
      console.error('Error saving response:', error)
      onError?.(error instanceof Error ? error.message : 'Unknown error')
      return false
    }
  }, [examId, sessionId, onError])

  const processSaveQueue = useCallback(async () => {
    if (isSaving.current || saveQueue.current.size === 0) {
      return
    }

    isSaving.current = true
    const itemsToSave = Array.from(saveQueue.current.entries())
    saveQueue.current.clear()

    try {
      // Process saves sequentially to avoid overwhelming the server
      for (const [questionId, data] of itemsToSave) {
        const success = await saveResponse(data)
        if (!success) {
          // Re-queue failed saves
          saveQueue.current.set(questionId, data)
        }
      }

      lastSaveTime.current = Date.now()
      onSave?.(true)
    } catch (error) {
      // Re-queue all items on error
      itemsToSave.forEach(([questionId, data]) => {
        saveQueue.current.set(questionId, data)
      })
      onSave?.(false, error instanceof Error ? error.message : 'Unknown error')
    } finally {
      isSaving.current = false
    }
  }, [saveResponse, onSave])

  // Auto-save interval
  useEffect(() => {
    autoSaveInterval.current = setInterval(() => {
      processSaveQueue()
    }, interval)

    return () => {
      if (autoSaveInterval.current) {
        clearInterval(autoSaveInterval.current)
      }
    }
  }, [interval, processSaveQueue])

  // Save on page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (saveQueue.current.size > 0) {
        // Attempt synchronous save
        processSaveQueue()
        
        // Show warning to user
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    const handleUnload = () => {
      // Final attempt to save using sendBeacon for reliability
      if (saveQueue.current.size > 0) {
        const token = localStorage.getItem('examToken')
        if (token) {
          const itemsToSave = Array.from(saveQueue.current.entries())
          itemsToSave.forEach(([questionId, data]) => {
            const payload = JSON.stringify({
              sessionId,
              ...data
            })
            
            navigator.sendBeacon(
              `/api/exam/${examId}/response`,
              new Blob([payload], { type: 'application/json' })
            )
          })
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('unload', handleUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('unload', handleUnload)
    }
  }, [examId, sessionId, processSaveQueue])

  // Save on visibility change (tab switch)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && saveQueue.current.size > 0) {
        processSaveQueue()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [processSaveQueue])

  const queueSave = useCallback((data: SaveData) => {
    saveQueue.current.set(data.questionId, data)
  }, [])

  const forceSave = useCallback(async (): Promise<boolean> => {
    await processSaveQueue()
    return saveQueue.current.size === 0
  }, [processSaveQueue])

  const getSaveStatus = useCallback(() => {
    return {
      pendingSaves: saveQueue.current.size,
      isSaving: isSaving.current,
      lastSaveTime: lastSaveTime.current,
      timeSinceLastSave: Date.now() - lastSaveTime.current
    }
  }, [])

  const clearQueue = useCallback(() => {
    saveQueue.current.clear()
  }, [])

  return {
    queueSave,
    forceSave,
    getSaveStatus,
    clearQueue,
    isSaving: isSaving.current,
    pendingSaves: saveQueue.current.size,
    lastSaveTime: lastSaveTime.current
  }
}

// Utility hook for tracking question time
export function useQuestionTimer() {
  const startTime = useRef<number>(Date.now())
  const totalTime = useRef<number>(0)

  const resetTimer = useCallback(() => {
    startTime.current = Date.now()
  }, [])

  const getTimeSpent = useCallback(() => {
    return Math.floor((Date.now() - startTime.current) / 1000)
  }, [])

  const getTotalTime = useCallback(() => {
    return totalTime.current + getTimeSpent()
  }, [getTimeSpent])

  const pauseTimer = useCallback(() => {
    totalTime.current += getTimeSpent()
    startTime.current = Date.now()
  }, [getTimeSpent])

  return {
    resetTimer,
    getTimeSpent,
    getTotalTime,
    pauseTimer
  }
}
