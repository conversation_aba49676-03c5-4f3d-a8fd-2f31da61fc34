const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testQuestionUpdate() {
  try {
    console.log('🧪 Testing Question Update Functionality...')
    
    // Find a question to test with
    const question = await prisma.question.findFirst({
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!question) {
      console.log('❌ No questions found in database')
      return
    }

    console.log('📋 Found Question to Test:')
    console.log(`   ID: ${question.id}`)
    console.log(`   Text: ${question.questionText.substring(0, 100)}...`)
    console.log(`   Type: ${question.questionType}`)
    console.log(`   Subject: ${question.subject.name}`)
    console.log(`   Marks: ${question.marks}`)
    console.log(`   Difficulty: ${question.difficulty}`)

    // Test the API endpoint
    console.log('\n🚀 Testing GET /api/questions/[id]...')
    
    try {
      const response = await fetch(`http://localhost:3001/api/questions/${question.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })
      
      console.log(`📡 GET Response Status: ${response.status} ${response.statusText}`)
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ GET request successful!')
        console.log('📄 Response data structure:', Object.keys(data))
        
        if (data.question) {
          console.log('✅ Question data retrieved successfully')
          console.log(`   Question ID: ${data.question.id}`)
          console.log(`   Question Text: ${data.question.questionText.substring(0, 50)}...`)
        }
      } else {
        const errorData = await response.json()
        console.log('❌ GET request failed!')
        console.log(`   Error: ${errorData.error}`)
      }
      
    } catch (fetchError) {
      console.log('❌ Network error during GET:', fetchError.message)
      console.log('   This might indicate the server is not running on port 3001')
    }

    // Test PUT request with sample data
    console.log('\n🔄 Testing PUT /api/questions/[id]...')
    
    const updateData = {
      questionText: question.questionText + ' (Updated)',
      questionType: question.questionType,
      options: question.options,
      correctAnswer: question.correctAnswer,
      marks: question.marks,
      difficulty: question.difficulty,
      explanation: question.explanation || 'Test explanation',
      subjectId: question.subject.id
    }

    try {
      const putResponse = await fetch(`http://localhost:3001/api/questions/${question.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(updateData)
      })
      
      console.log(`📡 PUT Response Status: ${putResponse.status} ${putResponse.statusText}`)
      
      if (putResponse.ok) {
        const putData = await putResponse.json()
        console.log('✅ PUT request successful!')
        console.log(`   Message: ${putData.message}`)
        
        // Revert the change
        const revertData = {
          ...updateData,
          questionText: question.questionText
        }
        
        await fetch(`http://localhost:3001/api/questions/${question.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(revertData)
        })
        
        console.log('✅ Changes reverted successfully')
        
      } else {
        const putErrorData = await putResponse.json()
        console.log('❌ PUT request failed!')
        console.log(`   Error: ${putErrorData.error}`)
        if (putErrorData.details) {
          console.log(`   Details: ${putErrorData.details}`)
        }
      }
      
    } catch (putError) {
      console.log('❌ Network error during PUT:', putError.message)
    }

    console.log('\n🎉 Question update test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testQuestionUpdate()
