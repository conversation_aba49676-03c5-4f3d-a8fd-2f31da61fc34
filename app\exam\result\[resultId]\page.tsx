'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useParams } from 'next/navigation'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Progress } from '@/app/components/ui/progress'
import { 
  CheckCircle, 
  XCircle, 
  Award, 
  Clock, 
  Target, 
  TrendingUp,
  ArrowLeft,
  Download,
  Share2,
  Loader2
} from 'lucide-react'

interface ExamResult {
  id: string
  marksObtained: number
  totalMarks: number
  percentage: number
  isPassed: boolean
  correctAnswers: number
  totalQuestions: number
  timeTaken: number
  submittedAt: string
  grade: string
  exam: {
    id: string
    title: string
    subject: {
      name: string
    }
    class: {
      name: string
    }
  }
}

export default function ExamResultPage() {
  const [result, setResult] = useState<ExamResult | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const router = useRouter()
  const params = useParams()
  const resultId = params.resultId as string

  useEffect(() => {
    const token = localStorage.getItem('examToken')
    if (!token) {
      router.push('/exam/login')
      return
    }

    fetchResult(token)
  }, [resultId, router])

  const fetchResult = async (token: string) => {
    try {
      const response = await fetch(`/api/exam/submit?resultId=${resultId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setResult(data.result)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch result')
      }
    } catch (error) {
      console.error('Error fetching result:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    }
    return `${minutes}m ${secs}s`
  }

  const getGradeColor = (grade: string): string => {
    switch (grade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'D':
        return 'bg-orange-100 text-orange-800 border-orange-300'
      default:
        return 'bg-red-100 text-red-800 border-red-300'
    }
  }

  const getPerformanceMessage = (percentage: number): string => {
    if (percentage >= 90) return 'Outstanding performance! 🎉'
    if (percentage >= 80) return 'Excellent work! 👏'
    if (percentage >= 70) return 'Good job! 👍'
    if (percentage >= 60) return 'Well done! 😊'
    if (percentage >= 50) return 'Keep improving! 💪'
    return 'Need more practice. Don\'t give up! 📚'
  }

  const handleGoBack = () => {
    router.push('/exam/select')
  }

  const handleDownloadResult = () => {
    // Implement PDF download functionality
    console.log('Download result as PDF')
  }

  const handleShareResult = () => {
    // Implement share functionality
    console.log('Share result')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading your result...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <Button onClick={handleGoBack} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Exams
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!result) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Result not found.</p>
          <Button onClick={handleGoBack} variant="outline" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exams
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <Button variant="ghost" onClick={handleGoBack} className="mb-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Exams
              </Button>
              <h1 className="text-3xl font-bold text-gray-900">Exam Result</h1>
              <p className="text-gray-600 mt-1">{result.exam.title}</p>
              <p className="text-sm text-gray-500">
                {result.exam.subject.name} • {result.exam.class.name}
              </p>
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={handleDownloadResult}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button variant="outline" onClick={handleShareResult}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Result Status */}
        <div className="mb-8">
          <Alert className={result.isPassed ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'}>
            {result.isPassed ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription className={result.isPassed ? 'text-green-800' : 'text-red-800'}>
              <span className="font-medium">
                {result.isPassed ? 'Congratulations! You passed the exam.' : 'You did not pass this exam.'}
              </span>
              <br />
              {getPerformanceMessage(result.percentage)}
            </AlertDescription>
          </Alert>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Score Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-blue-600" />
                <span>Score Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Grade */}
              <div className="text-center">
                <Badge className={`text-2xl px-4 py-2 ${getGradeColor(result.grade)}`}>
                  Grade {result.grade}
                </Badge>
              </div>

              {/* Percentage */}
              <div className="text-center">
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  {Math.round(result.percentage)}%
                </div>
                <Progress value={result.percentage} className="w-full h-3" />
              </div>

              {/* Marks */}
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-2xl font-bold text-green-600">
                    {result.marksObtained}
                  </div>
                  <div className="text-sm text-gray-600">Marks Obtained</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-2xl font-bold text-gray-900">
                    {result.totalMarks}
                  </div>
                  <div className="text-sm text-gray-600">Total Marks</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span>Performance Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Correct Answers</span>
                <span className="font-medium text-green-600">
                  {result.correctAnswers} / {result.totalQuestions}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600">Accuracy</span>
                <span className="font-medium">
                  {Math.round((result.correctAnswers / result.totalQuestions) * 100)}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>Time Taken</span>
                </span>
                <span className="font-medium">{formatTime(result.timeTaken)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center space-x-1">
                  <Target className="h-4 w-4" />
                  <span>Status</span>
                </span>
                <Badge variant={result.isPassed ? 'default' : 'destructive'}>
                  {result.isPassed ? 'Passed' : 'Failed'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600">Submitted At</span>
                <span className="font-medium text-sm">
                  {new Date(result.submittedAt).toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-center space-x-4">
          <Button onClick={handleGoBack} variant="outline" size="lg">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exams
          </Button>
          <Button onClick={handleDownloadResult} size="lg">
            <Download className="h-4 w-4 mr-2" />
            Download Certificate
          </Button>
        </div>
      </div>
    </div>
  )
}
