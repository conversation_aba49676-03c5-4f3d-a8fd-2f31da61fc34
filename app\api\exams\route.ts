import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { CreateExamRequest } from '@/app/types/exam'
import { cookies } from 'next/headers'

// POST - Create new exam
export async function POST(request: Request) {
  try {
    // Verify authentication using cookies (like other dashboard endpoints)
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      classId,
      subjectId,
      duration,
      totalMarks,
      passingMarks,
      instructions,
      startTime,
      endTime,
      allowRetake,
      shuffleQuestions,
      showResults,
      selectedQuestions
    } = body

    // Validate required fields
    if (!title || !classId || !subjectId || !duration || !selectedQuestions?.length) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate class and subject exist
    const [classExists, subjectExists] = await Promise.all([
      prisma.class.findUnique({ where: { id: classId } }),
      prisma.subject.findUnique({ where: { id: subjectId } })
    ])

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    if (!subjectExists) {
      return NextResponse.json(
        { error: 'Subject not found' },
        { status: 404 }
      )
    }

    // Validate questions exist
    const questions = await prisma.question.findMany({
      where: { id: { in: selectedQuestions } },
      select: { id: true, marks: true }
    })

    if (questions.length !== selectedQuestions.length) {
      return NextResponse.json(
        { error: 'Some selected questions do not exist' },
        { status: 400 }
      )
    }

    // Calculate total marks from selected questions
    const calculatedTotalMarks = questions.reduce((sum, q) => sum + q.marks, 0)

    // Create exam in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the exam
      const exam = await tx.exam.create({
        data: {
          title: title.trim(),
          description: description?.trim() || null,
          classId,
          subjectId,
          duration,
          totalMarks: calculatedTotalMarks,
          passingMarks: passingMarks || Math.floor(calculatedTotalMarks * 0.6),
          instructions: instructions?.trim() || null,
          startTime: startTime ? new Date(startTime) : null,
          endTime: endTime ? new Date(endTime) : null,
          allowRetake: !!allowRetake,
          shuffleQuestions: !!shuffleQuestions,
          showResults: !!showResults,
          isActive: true,
          createdBy: userData.id
        }
      })

      // Create exam questions
      const examQuestions = selectedQuestions.map((questionId: string, index: number) => {
        const question = questions.find(q => q.id === questionId)
        return {
          examId: exam.id,
          questionId,
          order: index + 1,
          marks: question?.marks || 1
        }
      })

      await tx.examQuestion.createMany({
        data: examQuestions
      })

      return exam
    })

    console.log(`Exam created: ${result.id} by ${userData.email}`)

    return NextResponse.json({
      success: true,
      exam: {
        id: result.id,
        title: result.title,
        description: result.description,
        duration: result.duration,
        totalMarks: result.totalMarks,
        passingMarks: result.passingMarks,
        questionsCount: selectedQuestions.length,
        createdAt: result.createdAt
      },
      message: 'Exam created successfully'
    })

  } catch (error) {
    console.error('Error creating exam:', error)
    return NextResponse.json(
      { error: 'Failed to create exam' },
      { status: 500 }
    )
  }
}

// GET - Fetch all exams with filters
export async function GET(request: Request) {
  try {
    // Verify authentication using cookies (like other dashboard endpoints)
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')
    const subjectId = searchParams.get('subjectId')
    const status = searchParams.get('status') // active, inactive, all
    const search = searchParams.get('search')

    console.log('Fetching exams with filters:', { classId, subjectId, status, search })

    // Build where clause
    const where: any = {}

    if (classId) {
      where.classId = classId
    }

    if (subjectId) {
      where.subjectId = subjectId
    }

    if (status === 'active') {
      where.isActive = true
    } else if (status === 'inactive') {
      where.isActive = false
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    const exams = await prisma.exam.findMany({
      where,
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        questions: {
          select: {
            id: true,
            marks: true
          }
        },
        sessions: {
          select: {
            id: true,
            status: true,
            isSubmitted: true
          }
        },
        _count: {
          select: {
            questions: true,
            sessions: true,
            results: true
          }
        },
        results: {
          select: {
            id: true,
            marksObtained: true,
            totalMarks: true,
            isPassed: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Add computed fields
    const examsWithStats = exams.map(exam => {
      const totalQuestions = exam.questions.length
      const totalSessions = exam.sessions.length
      const completedSessions = exam.sessions.filter(s => s.isSubmitted).length
      const activeSessions = exam.sessions.filter(s => s.status === 'IN_PROGRESS').length
      
      const results = exam.results
      const averageScore = results.length > 0 
        ? results.reduce((sum, r) => sum + (r.marksObtained / r.totalMarks * 100), 0) / results.length
        : 0
      
      const passRate = results.length > 0
        ? (results.filter(r => r.isPassed).length / results.length) * 100
        : 0

      return {
        ...exam,
        stats: {
          totalQuestions,
          totalSessions,
          completedSessions,
          activeSessions,
          averageScore: Math.round(averageScore * 100) / 100,
          passRate: Math.round(passRate * 100) / 100
        }
      }
    })

    console.log(`Found ${examsWithStats.length} exams`)

    return NextResponse.json({
      exams: examsWithStats,
      total: examsWithStats.length
    })

  } catch (error) {
    console.error('Error fetching exams:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exams' },
      { status: 500 }
    )
  }
}


