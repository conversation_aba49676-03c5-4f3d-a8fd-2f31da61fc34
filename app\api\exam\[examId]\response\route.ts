import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import { SubmitResponseRequest } from '@/app/types/exam'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

export async function POST(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId } = studentData
    const { examId } = params
    const body: SubmitResponseRequest = await request.json()
    const { sessionId, questionId, selectedAnswer, isFlagged = false, timeSpent = 0 } = body

    console.log(`Saving response for session ${sessionId}, question ${questionId}`)

    // Verify session exists and belongs to student
    const session = await prisma.examSession.findFirst({
      where: {
        id: sessionId,
        examId: examId,
        studentId: studentId,
        status: 'IN_PROGRESS'
      },
      include: {
        exam: true
      }
    })

    if (!session) {
      return NextResponse.json(
        { error: 'Invalid or inactive session' },
        { status: 404 }
      )
    }

    // Check if session has expired
    const currentTime = new Date()
    const timeElapsed = Math.floor((currentTime.getTime() - session.startTime.getTime()) / 1000)
    const timeRemaining = Math.max(0, (session.exam.duration * 60) - timeElapsed)

    if (timeRemaining <= 0) {
      // Session has expired, mark as expired
      await prisma.examSession.update({
        where: { id: session.id },
        data: { 
          status: 'EXPIRED',
          endTime: currentTime
        }
      })
      
      return NextResponse.json(
        { error: 'Exam session has expired' },
        { status: 400 }
      )
    }

    // Verify question belongs to this exam
    const examQuestion = await prisma.examQuestion.findFirst({
      where: {
        examId: examId,
        questionId: questionId
      }
    })

    if (!examQuestion) {
      return NextResponse.json(
        { error: 'Question not found in this exam' },
        { status: 404 }
      )
    }

    // Save or update response
    const response = await prisma.examResponse.upsert({
      where: {
        sessionId_questionId: {
          sessionId: sessionId,
          questionId: questionId
        }
      },
      update: {
        selectedAnswer: selectedAnswer || null,
        isFlagged,
        timeSpent,
        updatedAt: currentTime
      },
      create: {
        sessionId,
        questionId,
        selectedAnswer: selectedAnswer || null,
        isFlagged,
        timeSpent,
        responseTime: currentTime,
        updatedAt: currentTime
      }
    })

    // Update session's last activity
    await prisma.examSession.update({
      where: { id: sessionId },
      data: {
        updatedAt: currentTime
      }
    })

    console.log(`Response saved for question ${questionId}`)

    return NextResponse.json({
      success: true,
      message: 'Response saved successfully',
      timeRemaining
    })

  } catch (error) {
    console.error('Error saving response:', error)
    return NextResponse.json(
      { error: 'Failed to save response' },
      { status: 500 }
    )
  }
}

// GET - Retrieve responses for a session
export async function GET(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId } = studentData
    const { examId } = params
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Verify session exists and belongs to student
    const session = await prisma.examSession.findFirst({
      where: {
        id: sessionId,
        examId: examId,
        studentId: studentId
      }
    })

    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 404 }
      )
    }

    // Get all responses for this session
    const responses = await prisma.examResponse.findMany({
      where: {
        sessionId: sessionId
      },
      include: {
        question: {
          select: {
            id: true,
            questionText: true,
            questionType: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    return NextResponse.json({
      responses: responses.map(response => ({
        questionId: response.questionId,
        selectedAnswer: response.selectedAnswer,
        isFlagged: response.isFlagged,
        timeSpent: response.timeSpent,
        question: response.question
      }))
    })

  } catch (error) {
    console.error('Error fetching responses:', error)
    return NextResponse.json(
      { error: 'Failed to fetch responses' },
      { status: 500 }
    )
  }
}
