generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Attendance {
  id        String   @id @default(cuid())
  date      String
  className String
  studentId String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  student   Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([className, date])
  @@index([studentId])
  @@map("attendance")
}

model Class {
  id                 String              @id @default(cuid())
  name               String              @unique
  hrTeacherId        String?
  totalStudents      Int                 @default(0)
  totalSubjects      Int                 @default(0)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  hrTeacher          Teacher?            @relation("HeadTeacher", fields: [hrTeacherId], references: [id])
  classTeachers      ClassTeacher[]
  hrTeacherRecord    HRTeacher?
  students           Student[]
  subjects           Subject[]
  teacherAssignments TeacherAssignment[]
  exams              Exam[]
  questions          Question[]

  @@index([hrTeacherId])
  @@map("class")
}

model ClassTeacher {
  id        String   @id @default(cuid())
  classId   String
  teacherId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  class     Class    @relation(fields: [classId], references: [id])
  teacher   Teacher  @relation(fields: [teacherId], references: [id])

  @@index([classId])
  @@index([teacherId])
  @@map("classteacher")
}

model Mark {
  id           String   @id @default(cuid())
  studentId    String
  subject      String   @db.VarChar(100)
  marks        Int
  totalMarks   Int      @default(100)
  className    String   @db.VarChar(50)
  term         String   @db.VarChar(20)
  academicYear String   @default("2024-2025") @db.VarChar(20)
  remarks      String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  createdBy    String?
  creator      User?    @relation(fields: [createdBy], references: [id])
  student      Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([studentId, subject, className, term, academicYear])
  @@index([studentId])
  @@index([createdBy])
  @@index([className])
  @@index([subject])
  @@index([term])
  @@index([academicYear])
  @@map("mark")
}

model Student {
  id           String          @id @default(cuid())
  name         String
  className    String
  fatherName   String
  gfName       String
  age          Int
  gender       String
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  sid          String          @unique
  photoUrl     String?
  academicYear String          @default("2024-2025")
  attendance   Attendance[]
  marks        Mark[]
  parents      ParentStudent[]
  payments     Payment[]
  class        Class           @relation(fields: [className], references: [name])
  examSessions ExamSession[]
  examResults  ExamResult[]

  @@index([className])
  @@map("student")
}

model Subject {
  id                 String              @id @default(cuid())
  name               String              @db.VarChar(100)
  classId            String
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  class              Class               @relation(fields: [classId], references: [id])
  subjectTeachers    SubjectTeacher[]
  teacherAssignments TeacherAssignment[]
  exams              Exam[]
  questions          Question[]

  @@unique([name, classId])
  @@index([classId])
  @@map("subject")
}

model SubjectTeacher {
  id        String   @id @default(cuid())
  subjectId String
  teacherId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  subject   Subject  @relation(fields: [subjectId], references: [id])
  teacher   Teacher  @relation(fields: [teacherId], references: [id])

  @@index([subjectId])
  @@index([teacherId])
  @@map("subjectteacher")
}

model Teacher {
  id              String           @id @default(cuid())
  name            String
  fatherName      String
  gender          String
  email           String           @unique
  subject         String
  mobile          String
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  classesAsHR     Class[]          @relation("HeadTeacher")
  classTeachers   ClassTeacher[]
  hrTeachers      HRTeacher[]
  subjectTeachers SubjectTeacher[]

  @@map("teacher")
}

model HRTeacher {
  id        String   @id @default(cuid())
  teacherId String
  classId   String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  class     Class    @relation(fields: [classId], references: [id], onDelete: Cascade)
  teacher   Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@index([teacherId])
  @@index([classId])
  @@map("hrteacher")
}

model User {
  id                 String              @id @default(cuid())
  email              String              @unique
  password           String
  name               String
  role               UserRole
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  lastLogin          DateTime?
  status             String              @default("active")
  address            String?
  appNotifications   Boolean?            @default(true)
  bio                String?
  emailNotifications Boolean?            @default(true)
  language           String?             @default("english")
  phone              String?
  photoUrl           String?
  theme              String?             @default("system")
  createdMarks       Mark[]
  parentStudents     ParentStudent[]
  teacherAssignments TeacherAssignment[]

  @@map("user")
}

model ParentStudent {
  id        String   @id @default(cuid())
  parentId  String
  studentId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  parent    User     @relation(fields: [parentId], references: [id], onDelete: Cascade)
  student   Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([parentId, studentId])
  @@index([parentId])
  @@index([studentId])
  @@map("parent_student")
}

model HeroSlide {
  id        String   @id @default(cuid())
  title     String
  subtitle  String
  imageUrl  String
  ctaText   String
  ctaLink   String
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("hero_slide")
}

model Announcement {
  id        String   @id @default(cuid())
  title     String
  content   String
  date      DateTime
  type      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("announcement")
}

model QuickLink {
  id          String   @id @default(cuid())
  title       String
  description String
  icon        String
  url         String
  color       String
  borderColor String
  hoverColor  String
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("quick_link")
}

model FeaturedContent {
  id          String   @id @default(cuid())
  title       String
  description String
  imageUrl    String
  features    Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("featured_content")
}

model NewsEvent {
  id        String   @id @default(cuid())
  title     String
  content   String
  excerpt   String
  imageUrl  String
  date      DateTime
  category  String
  tags      String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("news_event")
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  role      String
  content   String
  imageUrl  String?
  rating    Int      @default(5)
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonial")
}

model CallToAction {
  id               String   @id @default(cuid())
  title            String
  description      String
  primaryBtnText   String
  primaryBtnLink   String
  secondaryBtnText String?
  secondaryBtnLink String?
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("call_to_action")
}

model Role {
  id            String           @id @default(cuid())
  name          RoleName         @unique
  description   String
  systemDefined Boolean          @default(true)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  permissions   RolePermission[]

  @@map("role")
}

model Permission {
  id          String           @id @default(cuid())
  name        String           @unique
  description String
  category    String
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  roles       RolePermission[]

  @@map("permission")
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
  @@map("rolepermission")
}

model TeacherAssignment {
  id          String   @id @default(cuid())
  teacherId   String
  classId     String?
  subjectId   String?
  isHRTeacher Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  class       Class?   @relation(fields: [classId], references: [id])
  subject     Subject? @relation(fields: [subjectId], references: [id])
  teacher     User     @relation(fields: [teacherId], references: [id])

  @@index([teacherId])
  @@index([classId])
  @@index([subjectId])
  @@map("teacherassignment")
}

model FeeType {
  id          String    @id @default(cuid())
  name        String    @unique
  description String
  amount      Float
  frequency   String
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  payments    Payment[]

  @@map("fee_type")
}

model Payment {
  id            String   @id @default(cuid())
  invoiceNumber String   @unique
  studentId     String
  feeTypeId     String
  amount        Float
  paymentDate   DateTime
  forMonth      String?
  paymentMethod String
  transferId    String?
  status        String
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  feeType       FeeType  @relation(fields: [feeTypeId], references: [id])
  student       Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([studentId])
  @@index([feeTypeId])
  @@index([forMonth])
  @@map("payment")
}

model BankPaymentVoucher {
  id            String   @id @default(cuid())
  voucherNo     String   @unique
  date          DateTime
  paidTo        String
  accountCode   String?
  amount        Float
  paymentMethod String
  chequeNo      String?
  accountNo     String?
  purpose       String
  status        String   @default("pending")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("bank_payment_voucher")
}

model AccountCode {
  id          String   @id @default(cuid())
  code        String   @unique
  description String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("account_code")
}

model JournalVoucher {
  id            String   @id @default(cuid())
  voucherNo     String   @unique
  date          DateTime
  paidTo        String
  accountCode   String
  amount        Float
  paymentMethod String
  chequeNo      String?
  purpose       String
  status        String   @default("pending")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("journal_voucher")
}

model PaymentReceipt {
  id              String   @id @default(cuid())
  studentName     String
  className       String
  month           String
  bankName        String
  receiptImageUrl String
  submittedAt     DateTime @default(now())
  status          String   @default("pending")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("payment_receipts")
}

model TeacherPermission {
  id                String   @id @default(cuid())
  teacherId         String
  classId           String
  canViewAttendance Boolean  @default(false)
  canTakeAttendance Boolean  @default(false)
  canAddMarks       Boolean  @default(false)
  canEditMarks      Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([teacherId, classId])
  @@index([teacherId])
  @@index([classId])
  @@map("teacher_permissions")
}

model PromotionPolicy {
  id                String   @id @default(cuid())
  passingScore      Int      @default(50)
  maxFailedSubjects Int      @default(2)
  applyToAllClasses Boolean  @default(true)
  isActive          Boolean  @default(true)
  description       String?
  createdBy         String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([isActive])
  @@map("promotion_policy")
}

model AppSettings {
  id            String   @id @default(cuid())
  schoolName    String   @default("School Management System")
  schoolLogo    String?
  semesterCount Int      @default(2)
  defaultAcademicYear String @default("2024-2025")
  sidebarBgColor String  @default("#1f2937")
  sidebarTextColor String @default("#ffffff")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("app_settings")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  SUPERVISOR
  ACCOUNTANT
  TEACHER
  PARENT
  UNIT_LEADER
  DATA_ENCODER
}

enum RoleName {
  SUPER_ADMIN
  ADMIN
  SUPERVISOR
  ACCOUNTANT
  TEACHER
  UNIT_LEADER
  DATA_ENCODER
}

// Online Exam System Models

model Exam {
  id              String    @id @default(cuid())
  title           String
  description     String?
  classId         String
  subjectId       String
  duration        Int       // Duration in minutes
  totalMarks      Int
  passingMarks    Int
  instructions    String?
  isActive        Boolean   @default(true)
  startTime       DateTime?
  endTime         DateTime?
  allowRetake     Boolean   @default(false)
  shuffleQuestions Boolean  @default(true)
  showResults     Boolean   @default(true)
  createdBy       String    // Teacher/Admin ID
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  class           Class     @relation(fields: [classId], references: [id], onDelete: Cascade)
  subject         Subject   @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  questions       ExamQuestion[]
  sessions        ExamSession[]
  results         ExamResult[]

  @@index([classId])
  @@index([subjectId])
  @@index([createdBy])
  @@index([isActive])
  @@map("exams")
}

model Question {
  id              String    @id @default(cuid())
  questionText    String
  questionType    QuestionType @default(MULTIPLE_CHOICE)
  options         Json      // Array of options for MCQ
  correctAnswer   String    // Correct option key (A, B, C, D)
  explanation     String?
  marks           Int       @default(1)
  difficulty      Difficulty @default(MEDIUM)
  subjectId       String
  classId         String
  tags            Json?     // For categorization (array of strings)
  isActive        Boolean   @default(true)
  createdBy       String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  subject         Subject   @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  class           Class     @relation(fields: [classId], references: [id], onDelete: Cascade)
  examQuestions   ExamQuestion[]
  responses       ExamResponse[]

  @@index([subjectId])
  @@index([classId])
  @@index([createdBy])
  @@index([isActive])
  @@map("questions")
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  FILL_BLANK
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

model ExamQuestion {
  id          String    @id @default(cuid())
  examId      String
  questionId  String
  order       Int       // Question order in exam
  marks       Int       // Marks for this question in this exam

  // Relations
  exam        Exam      @relation(fields: [examId], references: [id], onDelete: Cascade)
  question    Question  @relation(fields: [questionId], references: [id])

  @@unique([examId, questionId])
  @@index([examId])
  @@index([questionId])
  @@map("exam_questions")
}

model ExamSession {
  id              String    @id @default(cuid())
  examId          String
  studentId       String
  startTime       DateTime  @default(now())
  endTime         DateTime?
  duration        Int?      // Actual duration taken in minutes
  status          ExamStatus @default(IN_PROGRESS)
  ipAddress       String?
  userAgent       String?
  browserInfo     Json?     // Browser fingerprint
  isSubmitted     Boolean   @default(false)
  autoSubmitted   Boolean   @default(false)
  flaggedCount    Int       @default(0)
  warningCount    Int       @default(0)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  exam            Exam      @relation(fields: [examId], references: [id])
  student         Student   @relation(fields: [studentId], references: [id])
  responses       ExamResponse[]
  result          ExamResult?

  @@unique([examId, studentId]) // One session per student per exam
  @@index([examId])
  @@index([studentId])
  @@index([status])
  @@map("exam_sessions")
}

enum ExamStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  EXPIRED
  TERMINATED
}

model ExamResponse {
  id              String    @id @default(cuid())
  sessionId       String
  questionId      String
  selectedAnswer  String?   // Selected option (A, B, C, D)
  isCorrect       Boolean?
  marks           Int       @default(0)
  timeSpent       Int       @default(0) // Time in seconds
  isFlagged       Boolean   @default(false)
  isVisited       Boolean   @default(false)
  responseTime    DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  session         ExamSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  question        Question    @relation(fields: [questionId], references: [id])

  @@unique([sessionId, questionId])
  @@index([sessionId])
  @@index([questionId])
  @@map("exam_responses")
}

model ExamResult {
  id              String    @id @default(cuid())
  sessionId       String    @unique
  examId          String
  studentId       String
  totalQuestions  Int
  attemptedQuestions Int
  correctAnswers  Int
  wrongAnswers    Int
  marksObtained   Int
  totalMarks      Int
  percentage      Float
  grade           String?
  isPassed        Boolean
  timeTaken       Int       // Total time in minutes
  submittedAt     DateTime
  createdAt       DateTime  @default(now())

  // Relations
  session         ExamSession @relation(fields: [sessionId], references: [id])
  exam            Exam        @relation(fields: [examId], references: [id])
  student         Student     @relation(fields: [studentId], references: [id])

  @@index([examId])
  @@index([studentId])
  @@index([isPassed])
  @@map("exam_results")
}
