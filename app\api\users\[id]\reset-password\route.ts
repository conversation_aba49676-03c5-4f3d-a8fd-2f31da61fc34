import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'

// Helper function to generate a secure temporary password
function generateSecurePassword(): string {
  const length = 12
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''

  // Ensure at least one character from each category
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const numbers = '0123456789'
  const symbols = '!@#$%^&*'

  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += symbols[Math.floor(Math.random() * symbols.length)]

  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)]
  }

  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Initiating password reset for user ID: ${params.id}`)

    // Get request body
    const body = await request.json()
    const { method = 'email', reason = 'Password reset requested' } = body

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    let requesterId = 'system'
    let requesterEmail = '<EMAIL>'

    // Verify the token and get requester info
    try {
      const decoded = await verifyJWT(token)
      requesterId = decoded.id
      requesterEmail = decoded.email || '<EMAIL>'

      // Only allow SUPER_ADMIN to reset passwords
      if (decoded.role !== 'SUPER_ADMIN') {
        console.error('Unauthorized password reset attempt', {
          requesterId: decoded.id,
          requesterRole: decoded.role
        })
        return NextResponse.json(
          { error: 'Forbidden - Only Super Admins can reset passwords' },
          { status: 403 }
        )
      }
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError)
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: { id: true, email: true, name: true }
    })

    if (!user) {
      console.error(`User with ID ${params.id} not found`)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Log the password reset request for audit purposes
    console.log(`Password reset initiated for ${user.email} by ${requesterEmail}`, {
      targetUserId: user.id,
      targetUserEmail: user.email,
      requesterId,
      requesterEmail,
      method,
      reason,
      timestamp: new Date().toISOString()
    })

    if (method === 'email') {
      // Email reset method
      // In a real application, you would:
      // 1. Generate a secure password reset token
      // 2. Store it in the database with an expiration time
      // 3. Send an email to the user with a link containing the token

      console.log(`Password reset email would be sent to ${user.email}`)

      return NextResponse.json({
        message: 'Password reset email sent successfully',
        method: 'email',
        targetUser: {
          id: user.id,
          name: user.name,
          email: user.email
        },
        requestedBy: requesterEmail,
        reason
      })

    } else if (method === 'temporary_password') {
      // Temporary password method
      // Generate a secure temporary password
      const tempPassword = generateSecurePassword()

      // In a real app, you would:
      // 1. Hash the temporary password
      // 2. Update the user's password in the database
      // 3. Set a flag requiring password change on next login

      console.log(`Temporary password generated for ${user.email}: ${tempPassword}`)

      // Update the user with a flag to require password change
      await prisma.user.update({
        where: { id: params.id },
        data: {
          // In a real app, you would hash this password
          // password: await bcrypt.hash(tempPassword, 10),
          // requirePasswordChange: true,
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        message: 'Temporary password generated successfully',
        method: 'temporary_password',
        tempPassword: process.env.NODE_ENV === 'development' ? tempPassword : undefined,
        targetUser: {
          id: user.id,
          name: user.name,
          email: user.email
        },
        requestedBy: requesterEmail,
        reason,
        instructions: 'User must change password on next login'
      })

    } else {
      return NextResponse.json(
        { error: 'Invalid reset method. Must be "email" or "temporary_password"' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Error resetting password:', error)
    return NextResponse.json(
      { error: 'Failed to reset password' },
      { status: 500 }
    )
  }
}
