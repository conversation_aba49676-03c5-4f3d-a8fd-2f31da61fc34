'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Separator } from '@/app/components/ui/separator'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Clock, 
  Users, 
  FileText, 
  Target,
  Calendar,
  Award,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react'

interface ExamDetails {
  id: string
  title: string
  description?: string
  duration: number
  totalMarks: number
  passingMarks: number
  instructions?: string
  isActive: boolean
  startTime?: string
  endTime?: string
  allowRetake: boolean
  shuffleQuestions: boolean
  showResults: boolean
  createdAt: string
  updatedAt: string
  class: {
    id: string
    name: string
  }
  subject: {
    id: string
    name: string
  }
  questions: Array<{
    id: string
    order: number
    marks: number
    question: {
      id: string
      questionText: string
      questionType: string
      difficulty: string
      marks: number
      options?: any
    }
  }>
  sessions: Array<{
    id: string
    status: string
    isSubmitted: boolean
    startTime: string
    endTime?: string
    studentId: string
  }>
  results: Array<{
    id: string
    marksObtained: number
    totalMarks: number
    percentage: number
    isPassed: boolean
    submittedAt: string
    studentId: string
  }>
  _count: {
    questions: number
    sessions: number
    results: number
  }
}

export default function ExamDetailPage() {
  const router = useRouter()
  const params = useParams()
  const examId = params.examId as string

  const [exam, setExam] = useState<ExamDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  useEffect(() => {
    fetchExamDetails()
  }, [examId])

  const fetchExamDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/exams/${examId}`, {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to fetch exam details')
      }

      const data = await response.json()
      setExam(data.exam)
    } catch (error) {
      console.error('Error fetching exam details:', error)
      setError('Failed to load exam details')
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = async () => {
    if (!exam) return

    try {
      setActionLoading('toggle')
      const response = await fetch(`/api/exams/${examId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          isActive: !exam.isActive
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update exam status')
      }

      const data = await response.json()
      setExam(data.exam)
    } catch (error) {
      console.error('Error updating exam status:', error)
      setError('Failed to update exam status')
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async () => {
    if (!exam) return

    if (!confirm(`Are you sure you want to delete "${exam.title}"? This action cannot be undone.`)) {
      return
    }

    try {
      setActionLoading('delete')
      const response = await fetch(`/api/exams/${examId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete exam')
      }

      router.push('/dashboard/online-exam/exams')
    } catch (error: any) {
      console.error('Error deleting exam:', error)
      setError(error.message || 'Failed to delete exam')
    } finally {
      setActionLoading(null)
    }
  }

  const handleResetStatus = async () => {
    if (!exam) return

    if (!confirm(`Reset "${exam.title}" to active status? This will:\n\n• Make the exam active and available\n• Remove time restrictions\n• Enable retakes\n• Reset expired sessions\n\nContinue?`)) {
      return
    }

    try {
      setActionLoading('reset')
      const response = await fetch(`/api/exams/${examId}/reset-status`, {
        method: 'POST',
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to reset exam status')
      }

      const result = await response.json()
      setSuccessMessage('Exam status reset successfully! The exam is now active and available to students.')

      // Refresh exam data
      fetchExamDetails()

      // Clear success message after 5 seconds
      setTimeout(() => setSuccessMessage(null), 5000)

    } catch (error: any) {
      console.error('Error resetting exam status:', error)
      setError(error.message || 'Failed to reset exam status')
    } finally {
      setActionLoading(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">In Progress</Badge>
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>
      case 'EXPIRED':
        return <Badge variant="default" className="bg-red-100 text-red-800">Expired</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          <Alert>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    )
  }

  if (!exam) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          <Alert>
            <AlertDescription>Exam not found</AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{exam.title}</h1>
            <p className="text-gray-600">{exam.class.name} • {exam.subject.name}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/dashboard/online-exam/exams/${examId}/edit`)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          
          <Button
            variant="outline"
            onClick={handleToggleStatus}
            disabled={actionLoading === 'toggle'}
            className={exam.isActive ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}
          >
            {actionLoading === 'toggle' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : exam.isActive ? (
              <Pause className="h-4 w-4 mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {exam.isActive ? 'Deactivate' : 'Activate'}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleResetStatus}
            disabled={actionLoading === 'reset'}
            className="text-blue-600 hover:text-blue-700"
          >
            {actionLoading === 'reset' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            )}
            Reset Status
          </Button>

          <Button
            variant="outline"
            onClick={handleDelete}
            disabled={actionLoading === 'delete'}
            className="text-red-600 hover:text-red-700"
          >
            {actionLoading === 'delete' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4 mr-2" />
            )}
            Delete
          </Button>
        </div>
      </div>

      {error && (
        <Alert>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert className="border-green-200 bg-green-50">
          <div className="flex items-center space-x-2">
            <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <AlertDescription className="text-green-800">{successMessage}</AlertDescription>
          </div>
        </Alert>
      )}

      {/* Status Badge */}
      <div className="flex items-center space-x-2">
        {exam.isActive ? (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        ) : (
          <Badge variant="secondary">
            <XCircle className="h-3 w-3 mr-1" />
            Inactive
          </Badge>
        )}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Exam Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Exam Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {exam.description && (
                <div>
                  <h4 className="font-medium text-gray-900">Description</h4>
                  <p className="text-gray-600">{exam.description}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900">Duration</h4>
                  <p className="text-gray-600 flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {exam.duration} minutes
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Total Marks</h4>
                  <p className="text-gray-600 flex items-center">
                    <Award className="h-4 w-4 mr-1" />
                    {exam.totalMarks} marks
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Passing Marks</h4>
                  <p className="text-gray-600 flex items-center">
                    <Target className="h-4 w-4 mr-1" />
                    {exam.passingMarks} marks
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Questions</h4>
                  <p className="text-gray-600 flex items-center">
                    <FileText className="h-4 w-4 mr-1" />
                    {exam._count.questions} questions
                  </p>
                </div>
              </div>

              {exam.instructions && (
                <div>
                  <h4 className="font-medium text-gray-900">Instructions</h4>
                  <p className="text-gray-600 whitespace-pre-wrap">{exam.instructions}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Statistics */}
        <div className="space-y-6">
          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Sessions:</span>
                <span className="font-medium">{exam._count.sessions}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Completed Results:</span>
                <span className="font-medium">{exam._count.results}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pass Rate:</span>
                <span className="font-medium">
                  {exam._count.results > 0 && exam.results && exam.results.length > 0
                    ? `${Math.round((exam.results.filter(r => r.isPassed).length / exam._count.results) * 100)}%`
                    : 'N/A'
                  }
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Allow Retake:</span>
                {exam.allowRetake ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Shuffle Questions:</span>
                {exam.shuffleQuestions ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Show Results:</span>
                {exam.showResults ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card>
            <CardHeader>
              <CardTitle>Timestamps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <span className="text-gray-600 block">Created:</span>
                <span className="text-sm">{formatDate(exam.createdAt)}</span>
              </div>
              <div>
                <span className="text-gray-600 block">Last Updated:</span>
                <span className="text-sm">{formatDate(exam.updatedAt)}</span>
              </div>
              {exam.startTime && (
                <div>
                  <span className="text-gray-600 block">Start Time:</span>
                  <span className="text-sm">{formatDate(exam.startTime)}</span>
                </div>
              )}
              {exam.endTime && (
                <div>
                  <span className="text-gray-600 block">End Time:</span>
                  <span className="text-sm">{formatDate(exam.endTime)}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </DashboardLayout>
  )
}
