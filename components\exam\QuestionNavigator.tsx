'use client'

import { useState } from 'react'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { ScrollArea } from '@/app/components/ui/scroll-area'
import {
  CheckCircle,
  Circle,
  Flag,
  Eye,
  X,
  ChevronLeft,
  ChevronRight,
  LayoutGrid
} from 'lucide-react'
import { QuestionNavigatorItem } from '@/app/types/exam'

interface QuestionNavigatorProps {
  questions: QuestionNavigatorItem[]
  currentQuestionIndex: number
  onQuestionSelect: (index: number) => void
  onClose?: () => void
  isVisible: boolean
  className?: string
}

export default function QuestionNavigator({
  questions,
  currentQuestionIndex,
  onQuestionSelect,
  onClose,
  isVisible,
  className = ''
}: QuestionNavigatorProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  if (!isVisible) return null

  const getStatusIcon = (status: string, isCurrent: boolean) => {
    const iconClass = `h-4 w-4 ${isCurrent ? 'text-white' : ''}`
    
    switch (status) {
      case 'answered':
        return <CheckCircle className={iconClass} />
      case 'flagged':
        return <Flag className={iconClass} />
      case 'visited':
        return <Eye className={iconClass} />
      default:
        return <Circle className={iconClass} />
    }
  }

  const getStatusColor = (status: string, isCurrent: boolean) => {
    if (isCurrent) return 'bg-blue-600 text-white border-blue-600'
    
    switch (status) {
      case 'answered':
        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200'
      case 'flagged':
        return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200'
      case 'visited':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'answered':
        return 'Answered'
      case 'flagged':
        return 'Flagged'
      case 'visited':
        return 'Visited'
      default:
        return 'Not Visited'
    }
  }

  const getStatusCounts = () => {
    return questions.reduce((acc, question) => {
      acc[question.status] = (acc[question.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  const statusCounts = getStatusCounts()

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      onQuestionSelect(currentQuestionIndex - 1)
    }
  }

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      onQuestionSelect(currentQuestionIndex + 1)
    }
  }

  return (
    <Card className={`${className} shadow-lg`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center space-x-2">
            <LayoutGrid className="h-5 w-5" />
            <span>Question Navigator</span>
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Status Summary */}
        <div className="flex flex-wrap gap-2 mt-3">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">
            <CheckCircle className="h-3 w-3 mr-1" />
            Answered: {statusCounts.answered || 0}
          </Badge>
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">
            <Flag className="h-3 w-3 mr-1" />
            Flagged: {statusCounts.flagged || 0}
          </Badge>
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
            <Eye className="h-3 w-3 mr-1" />
            Visited: {statusCounts.visited || 0}
          </Badge>
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-300">
            <Circle className="h-3 w-3 mr-1" />
            Not Visited: {statusCounts['not-visited'] || 0}
          </Badge>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between mt-3">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevious}
              disabled={currentQuestionIndex === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNext}
              disabled={currentQuestionIndex === questions.length - 1}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
          
          <div className="text-sm text-gray-600">
            Question {currentQuestionIndex + 1} of {questions.length}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-64">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-5 gap-2">
              {questions.map((question, index) => (
                <Button
                  key={question.questionId}
                  variant="outline"
                  size="sm"
                  className={`h-12 w-12 p-0 border-2 transition-all duration-200 ${getStatusColor(question.status, question.isCurrent)}`}
                  onClick={() => onQuestionSelect(index)}
                  title={`Question ${question.order}: ${getStatusLabel(question.status)}`}
                >
                  <div className="flex flex-col items-center space-y-1">
                    {getStatusIcon(question.status, question.isCurrent)}
                    <span className="text-xs font-medium">{question.order}</span>
                  </div>
                </Button>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {questions.map((question, index) => (
                <Button
                  key={question.questionId}
                  variant="outline"
                  className={`w-full justify-start h-auto p-3 border-2 transition-all duration-200 ${getStatusColor(question.status, question.isCurrent)}`}
                  onClick={() => onQuestionSelect(index)}
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(question.status, question.isCurrent)}
                    <div className="flex-1 text-left">
                      <div className="font-medium">Question {question.order}</div>
                      <div className="text-xs opacity-75">{getStatusLabel(question.status)}</div>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          )}
        </ScrollArea>

        {/* Legend */}
        <div className="mt-4 pt-3 border-t">
          <div className="text-xs text-gray-600 mb-2">Legend:</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span>Answered</span>
            </div>
            <div className="flex items-center space-x-2">
              <Flag className="h-3 w-3 text-red-600" />
              <span>Flagged for Review</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="h-3 w-3 text-yellow-600" />
              <span>Visited</span>
            </div>
            <div className="flex items-center space-x-2">
              <Circle className="h-3 w-3 text-gray-600" />
              <span>Not Visited</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
