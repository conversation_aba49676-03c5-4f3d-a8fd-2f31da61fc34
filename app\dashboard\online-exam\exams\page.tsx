'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Badge } from '@/app/components/ui/badge'
import {
  Plus,
  Search,
  Filter,
  FileText,
  Clock,
  Users,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  Calendar,
  Award,
  Loader2
} from 'lucide-react'
import Link from 'next/link'

interface Exam {
  id: string
  title: string
  description: string
  duration: number
  totalMarks: number
  passingMarks: number
  isActive: boolean
  startTime: string | null
  endTime: string | null
  class: { name: string }
  subject: { name: string }
  _count: {
    questions: number
    sessions: number
  }
  createdAt: string
}

export default function ExamsPage() {
  const router = useRouter()
  const [search, setSearch] = useState('')
  const [filters, setFilters] = useState({
    classId: 'all',
    subjectId: 'all',
    status: 'all'
  })
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Fetch classes for filter
  const { data: classes } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const response = await fetch('/api/classes', {
        credentials: 'include' // Include cookies for authentication
      })
      if (!response.ok) throw new Error('Failed to fetch classes')
      return response.json()
    }
  })

  // Fetch subjects for filter
  const { data: subjects } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await fetch('/api/subjects', {
        credentials: 'include' // Include cookies for authentication
      })
      if (!response.ok) throw new Error('Failed to fetch subjects')
      return response.json()
    }
  })

  // Fetch exams
  const { data: examsData, isLoading, refetch } = useQuery({
    queryKey: ['exams', search, filters],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (search) params.append('search', search)
      if (filters.classId && filters.classId !== 'all') params.append('classId', filters.classId)
      if (filters.subjectId && filters.subjectId !== 'all') params.append('subjectId', filters.subjectId)
      if (filters.status && filters.status !== 'all') params.append('status', filters.status)

      const response = await fetch(`/api/exams?${params}`, {
        credentials: 'include' // Include cookies for authentication
      })
      if (!response.ok) throw new Error('Failed to fetch exams')
      return response.json()
    }
  })

  // Handler functions for exam actions
  const handleViewDetail = (examId: string) => {
    router.push(`/dashboard/online-exam/exams/${examId}`)
  }

  const handleEditExam = (examId: string) => {
    router.push(`/dashboard/online-exam/exams/${examId}/edit`)
  }

  const handleToggleStatus = async (examId: string, currentStatus: boolean) => {
    try {
      setActionLoading(`toggle-${examId}`)
      const response = await fetch(`/api/exams/${examId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          isActive: !currentStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update exam status')
      }

      // Refetch exams to update the list
      exams.refetch()
    } catch (error) {
      console.error('Error updating exam status:', error)
      alert('Failed to update exam status')
    } finally {
      setActionLoading(null)
    }
  }

  const handleDeleteExam = async (examId: string, examTitle: string) => {
    if (!confirm(`Are you sure you want to delete "${examTitle}"? This action cannot be undone.`)) {
      return
    }

    try {
      setActionLoading(`delete-${examId}`)
      const response = await fetch(`/api/exams/${examId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete exam')
      }

      // Refetch exams to update the list
      exams.refetch()
    } catch (error: any) {
      console.error('Error deleting exam:', error)
      alert(error.message || 'Failed to delete exam')
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusBadge = (exam: Exam) => {
    const now = new Date()
    const startTime = exam.startTime ? new Date(exam.startTime) : null
    const endTime = exam.endTime ? new Date(exam.endTime) : null

    if (!exam.isActive) {
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>
    }

    if (endTime && endTime < now) {
      return <Badge variant="outline" className="bg-red-100 text-red-800">Ended</Badge>
    }

    if (startTime && startTime > now) {
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Scheduled</Badge>
    }

    return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
  }

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Exam Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Create and manage online examinations
            </p>
          </div>
          
          <Link href="/dashboard/online-exam/exams/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Exam
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Input
                  placeholder="Search exams..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full"
                />
              </div>
              
              <Select 
                value={filters.classId} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, classId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes?.map((cls: any) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select 
                value={filters.subjectId} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, subjectId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects?.map((subject: any) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select 
                value={filters.status} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Exams List */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading exams...</p>
            </div>
          ) : examsData?.exams?.length > 0 ? (
            <>
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Found {examsData.exams.length} exam{examsData.exams.length !== 1 ? 's' : ''}
                </p>
              </div>
              
              <div className="grid gap-4">
                {examsData.exams.map((exam: Exam) => (
                  <Card key={exam.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-start space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg">
                              <FileText className="h-5 w-5 text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900">{exam.title}</h3>
                                {getStatusBadge(exam)}
                              </div>
                              
                              {exam.description && (
                                <p className="text-gray-600 text-sm mb-3">{exam.description}</p>
                              )}
                              
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                                <div className="flex items-center space-x-2">
                                  <Users className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm text-gray-600">{exam.class.name}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <FileText className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm text-gray-600">{exam.subject.name}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Clock className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm text-gray-600">{formatDuration(exam.duration)}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Award className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm text-gray-600">{exam.totalMarks} marks</span>
                                </div>
                              </div>
                              
                              <div className="flex items-center space-x-6 text-sm text-gray-500">
                                <span>{exam._count.questions} question{exam._count.questions !== 1 ? 's' : ''}</span>
                                <span>•</span>
                                <span>{exam._count.sessions} session{exam._count.sessions !== 1 ? 's' : ''}</span>
                                <span>•</span>
                                <span>Pass: {exam.passingMarks} marks</span>
                              </div>
                              
                              {(exam.startTime || exam.endTime) && (
                                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                    <div className="flex items-center space-x-2">
                                      <Calendar className="h-4 w-4 text-gray-400" />
                                      <span className="text-gray-600">Start: {formatDateTime(exam.startTime)}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <Calendar className="h-4 w-4 text-gray-400" />
                                      <span className="text-gray-600">End: {formatDateTime(exam.endTime)}</span>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            title="View Details"
                            onClick={() => handleViewDetail(exam.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            title="Edit Exam"
                            onClick={() => handleEditExam(exam.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            title={exam.isActive ? "Deactivate" : "Activate"}
                            className={exam.isActive ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}
                            onClick={() => handleToggleStatus(exam.id, exam.isActive)}
                            disabled={actionLoading === `toggle-${exam.id}`}
                          >
                            {actionLoading === `toggle-${exam.id}` ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : exam.isActive ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            title="Delete Exam"
                            onClick={() => handleDeleteExam(exam.id, exam.title)}
                            disabled={actionLoading === `delete-${exam.id}`}
                          >
                            {actionLoading === `delete-${exam.id}` ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No exams found</h3>
                <p className="text-gray-600 mb-4">
                  {search || Object.values(filters).some(f => f) 
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first exam'
                  }
                </p>
                <Link href="/dashboard/online-exam/exams/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Exam
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
