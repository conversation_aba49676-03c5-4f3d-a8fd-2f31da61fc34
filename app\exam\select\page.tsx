'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Loader2, Clock, FileText, Target, Calendar, LogOut, User, BookOpen } from 'lucide-react'

interface Student {
  id: string
  name: string
  sid: string
  className: string
}

interface ExamData {
  id: string
  title: string
  description?: string
  duration: number
  totalMarks: number
  passingMarks: number
  questionCount: number
  subject: {
    id: string
    name: string
  }
  class: {
    id: string
    name: string
  }
  startTime?: string
  endTime?: string
  status: string
  canStart: boolean
  message?: string
  existingSessionId?: string
}

export default function ExamSelectPage() {
  const [student, setStudent] = useState<Student | null>(null)
  const [exams, setExams] = useState<ExamData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    // Check authentication and load student data
    const token = localStorage.getItem('examToken')
    const studentData = localStorage.getItem('examStudent')

    if (!token || !studentData) {
      router.push('/exam/login')
      return
    }

    try {
      const parsedStudent = JSON.parse(studentData)
      setStudent(parsedStudent)
      fetchAvailableExams(token)
    } catch (error) {
      console.error('Error parsing student data:', error)
      router.push('/exam/login')
    }
  }, [router])

  const fetchAvailableExams = async (token: string) => {
    try {
      const response = await fetch('/api/exam/available', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setExams(data.exams || [])
      } else {
        setError('Failed to fetch available exams')
      }
    } catch (error) {
      console.error('Error fetching exams:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('examToken')
    localStorage.removeItem('examStudent')
    router.push('/exam/login')
  }

  const handleStartExam = (examId: string) => {
    router.push(`/exam/instructions/${examId}`)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-100 text-green-800">Available</Badge>
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800">Completed</Badge>
      case 'expired':
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>
      case 'not_started':
        return <Badge className="bg-yellow-100 text-yellow-800">Not Started</Badge>
      case 'ended':
        return <Badge className="bg-red-100 text-red-800">Ended</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading available exams...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-gray-600" />
                <span className="font-medium text-gray-900">{student?.name}</span>
                <span className="text-gray-500">({student?.sid})</span>
              </div>
              <div className="flex items-center space-x-2">
                <BookOpen className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">{student?.className}</span>
              </div>
            </div>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Available Exams</h1>
          <p className="text-gray-600">Select an exam to begin. Make sure you have a stable internet connection.</p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Exams Grid */}
        {exams.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Exams Available</h3>
              <p className="text-gray-600">There are currently no exams available for your class.</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {exams.map((exam) => (
              <Card key={exam.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <CardTitle className="text-lg">{exam.title}</CardTitle>
                    {getStatusBadge(exam.status)}
                  </div>
                  <CardDescription className="text-sm">
                    {exam.subject.name} • {exam.class.name}
                  </CardDescription>
                  {exam.description && (
                    <p className="text-sm text-gray-600 mt-2">{exam.description}</p>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Exam Details */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span>{exam.duration} minutes</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span>{exam.questionCount} questions</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-gray-500" />
                        <span>{exam.totalMarks} marks</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-gray-500" />
                        <span>Pass: {exam.passingMarks}</span>
                      </div>
                    </div>

                    {/* Time Restrictions */}
                    {(exam.startTime || exam.endTime) && (
                      <div className="text-xs text-gray-600 space-y-1">
                        {exam.startTime && (
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>Available from: {new Date(exam.startTime).toLocaleString()}</span>
                          </div>
                        )}
                        {exam.endTime && (
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>Available until: {new Date(exam.endTime).toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Status Message */}
                    {exam.message && (
                      <p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                        {exam.message}
                      </p>
                    )}

                    {/* Action Button */}
                    <Button
                      onClick={() => handleStartExam(exam.id)}
                      disabled={!exam.canStart}
                      className="w-full"
                      variant={exam.status === 'in_progress' ? 'default' : 'default'}
                    >
                      {exam.status === 'in_progress' ? 'Continue Exam' : 'Start Exam'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
