import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

// GET - Fetch individual exam details for students or dashboard
export async function GET(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Check if this is a dashboard request (has cookies) or student request (has authorization header)
    const cookieStore = cookies()
    const token = cookieStore.get('token')
    const authHeader = request.headers.get('authorization')

    // Dashboard request (cookie-based authentication)
    if (token && !authHeader) {
      return await handleDashboardRequest(params.examId, token.value)
    }

    // Student request (bearer token authentication)
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return await handleStudentRequest(params.examId, authHeader)
    }

    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  } catch (error) {
    console.error('Error in GET request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle dashboard requests
async function handleDashboardRequest(examId: string, tokenValue: string) {
  try {
    let userData: any

    try {
      userData = await verifyJWT(tokenValue)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Fetch exam details with all related data for dashboard
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        questions: {
          include: {
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                difficulty: true,
                marks: true,
                options: true
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        },
        sessions: {
          select: {
            id: true,
            status: true,
            isSubmitted: true,
            startTime: true,
            endTime: true,
            studentId: true
          }
        },
        results: {
          select: {
            id: true,
            marksObtained: true,
            totalMarks: true,
            percentage: true,
            isPassed: true,
            submittedAt: true,
            studentId: true
          }
        },
        _count: {
          select: {
            questions: true,
            sessions: true,
            results: true
          }
        }
      }
    })

    if (!exam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    console.log(`Successfully fetched exam details for dashboard: ${exam.title}`)
    return NextResponse.json({ exam })

  } catch (error) {
    console.error('Error fetching exam details for dashboard:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exam details' },
      { status: 500 }
    )
  }
}

// Handle student requests (existing functionality)
async function handleStudentRequest(examId: string, authHeader: string) {
  try {
    // Verify student authentication
    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId, className } = studentData

    console.log(`Fetching exam details for exam: ${examId}, student: ${studentId}`)

    // Get student's class ID
    const studentClass = await prisma.class.findUnique({
      where: { name: className },
      select: { id: true }
    })

    if (!studentClass) {
      return NextResponse.json(
        { error: 'Student class not found' },
        { status: 404 }
      )
    }
    // Fetch exam details
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        questions: {
          select: {
            id: true,
            order: true,
            marks: true,
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                difficulty: true,
                marks: true
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        },
        sessions: {
          where: {
            studentId: studentId
          },
          select: {
            id: true,
            status: true,
            isSubmitted: true,
            startTime: true,
            endTime: true
          }
        },
        _count: {
          select: {
            questions: true
          }
        }
      }
    })

    if (!exam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    // Check if exam is active
    if (!exam.isActive) {
      return NextResponse.json(
        { error: 'Exam is not active' },
        { status: 400 }
      )
    }

    // Check if student's class matches exam class
    if (exam.classId !== studentClass.id) {
      return NextResponse.json(
        { error: 'You are not authorized to access this exam' },
        { status: 403 }
      )
    }

    // Check time restrictions
    const currentTime = new Date()
    
    if (exam.startTime && exam.startTime > currentTime) {
      return NextResponse.json(
        { error: 'Exam has not started yet' },
        { status: 400 }
      )
    }

    if (exam.endTime && exam.endTime < currentTime) {
      return NextResponse.json(
        { error: 'Exam has ended' },
        { status: 400 }
      )
    }

    // Format response
    const response = {
      exam: {
        id: exam.id,
        title: exam.title,
        description: exam.description,
        duration: exam.duration,
        totalMarks: exam.totalMarks,
        passingMarks: exam.passingMarks,
        instructions: exam.instructions,
        startTime: exam.startTime,
        endTime: exam.endTime,
        allowRetake: exam.allowRetake,
        shuffleQuestions: exam.shuffleQuestions,
        showResults: exam.showResults,
        class: exam.class,
        subject: exam.subject,
        questionsCount: exam._count.questions,
        questions: exam.questions.map(eq => ({
          id: eq.question.id,
          questionText: eq.question.questionText,
          questionType: eq.question.questionType,
          difficulty: eq.question.difficulty,
          marks: eq.marks,
          order: eq.order
        })),
        sessions: exam.sessions
      }
    }

    console.log(`Successfully fetched exam: ${exam.title}`)
    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching exam details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exam details' },
      { status: 500 }
    )
  }
}

// PUT - Update exam (dashboard only)
export async function PUT(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { examId } = params
    const body = await request.json()

    // Check if exam exists
    const existingExam = await prisma.exam.findUnique({
      where: { id: examId }
    })

    if (!existingExam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    // Validate class-subject relationship if both are provided
    if (body.classId && body.subjectId) {
      const subject = await prisma.subject.findUnique({
        where: { id: body.subjectId },
        select: { classId: true, name: true }
      })

      if (!subject) {
        return NextResponse.json(
          { error: 'Selected subject not found' },
          { status: 400 }
        )
      }

      if (subject.classId !== body.classId) {
        return NextResponse.json(
          { error: 'Selected subject does not belong to the selected class' },
          { status: 400 }
        )
      }
    }

    // Update exam
    const updatedExam = await prisma.exam.update({
      where: { id: examId },
      data: {
        ...body,
        updatedAt: new Date()
      },
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            questions: true,
            sessions: true,
            results: true
          }
        }
      }
    })

    console.log(`Successfully updated exam: ${updatedExam.title}`)
    return NextResponse.json({
      message: 'Exam updated successfully',
      exam: updatedExam
    })

  } catch (error) {
    console.error('Error updating exam:', error)
    return NextResponse.json(
      { error: 'Failed to update exam' },
      { status: 500 }
    )
  }
}

// DELETE - Delete exam (dashboard only)
export async function DELETE(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check permissions - only SUPER_ADMIN and ADMIN can delete exams
    if (!['SUPER_ADMIN', 'ADMIN'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete exam' },
        { status: 403 }
      )
    }

    const { examId } = params

    // Check if exam exists
    const existingExam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        sessions: true,
        results: true
      }
    })

    if (!existingExam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    // Check if exam has sessions or results
    if (existingExam.sessions.length > 0 || existingExam.results.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete exam with existing sessions or results. Deactivate instead.' },
        { status: 400 }
      )
    }

    // Delete exam and related data in transaction
    await prisma.$transaction(async (tx) => {
      // Delete exam questions first
      await tx.examQuestion.deleteMany({
        where: { examId: examId }
      })

      // Delete exam
      await tx.exam.delete({
        where: { id: examId }
      })
    })

    console.log(`Successfully deleted exam: ${existingExam.title}`)
    return NextResponse.json({
      message: 'Exam deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting exam:', error)
    return NextResponse.json(
      { error: 'Failed to delete exam' },
      { status: 500 }
    )
  }
}
