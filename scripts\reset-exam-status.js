const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function resetExamStatus() {
  try {
    console.log('🔍 Checking exam status...')
    
    // Find the Grade 9 Mathematics Demo Exam
    const exam = await prisma.exam.findFirst({
      where: {
        title: 'Grade 9 Mathematics Demo Exam'
      },
      include: {
        sessions: {
          select: {
            id: true,
            status: true,
            isSubmitted: true,
            studentId: true,
            startTime: true,
            endTime: true
          }
        },
        class: {
          select: {
            name: true
          }
        },
        subject: {
          select: {
            name: true
          }
        }
      }
    })

    if (!exam) {
      console.log('❌ Exam not found')
      return
    }

    console.log('📋 Current Exam Status:')
    console.log(`   Title: ${exam.title}`)
    console.log(`   Class: ${exam.class.name}`)
    console.log(`   Subject: ${exam.subject.name}`)
    console.log(`   Active: ${exam.isActive}`)
    console.log(`   Start Time: ${exam.startTime || 'None'}`)
    console.log(`   End Time: ${exam.endTime || 'None'}`)
    console.log(`   Allow Retake: ${exam.allowRetake}`)
    console.log(`   Sessions: ${exam.sessions.length}`)

    if (exam.sessions.length > 0) {
      console.log('📝 Session Details:')
      exam.sessions.forEach((session, index) => {
        console.log(`   Session ${index + 1}: ${session.status} (Submitted: ${session.isSubmitted})`)
      })
    }

    console.log('\n🔧 Resetting exam status...')

    // Reset exam to active state
    const currentTime = new Date()

    const result = await prisma.$transaction(async (tx) => {
      // 1. Update exam to be active with no time restrictions
      const updatedExam = await tx.exam.update({
        where: { id: exam.id },
        data: {
          isActive: true,
          startTime: null, // Remove start time restriction
          endTime: null,   // Remove end time restriction
          allowRetake: true, // Enable retakes
          updatedAt: currentTime
        }
      })

      // 2. Reset any expired sessions
      const expiredSessions = exam.sessions.filter(s => s.status === 'EXPIRED')
      if (expiredSessions.length > 0) {
        await tx.examSession.updateMany({
          where: {
            examId: exam.id,
            status: 'EXPIRED'
          },
          data: {
            status: 'COMPLETED',
            endTime: currentTime
          }
        })
        console.log(`   ✅ Reset ${expiredSessions.length} expired sessions`)
      }

      return updatedExam
    })

    console.log('✅ Exam status reset successfully!')
    console.log('📋 New Status:')
    console.log(`   Active: ${result.isActive}`)
    console.log(`   Start Time: ${result.startTime || 'None (No restriction)'}`)
    console.log(`   End Time: ${result.endTime || 'None (No restriction)'}`)
    console.log(`   Allow Retake: ${result.allowRetake}`)
    console.log('\n🎉 The exam is now active and available to students!')

  } catch (error) {
    console.error('❌ Error resetting exam status:', error)
  } finally {
    await prisma.$disconnect()
  }
}

resetExamStatus()
