import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to view analytics
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const examId = searchParams.get('examId')
    const classId = searchParams.get('classId')
    const subjectId = searchParams.get('subjectId')
    const dateRange = searchParams.get('range') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()

    switch (dateRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Build where clause for filtering
    const whereClause: any = {
      createdAt: {
        gte: startDate,
        lte: now
      }
    }

    if (examId) whereClause.examId = examId
    if (classId) whereClause.exam = { classId }
    if (subjectId) whereClause.exam = { subjectId }

    // Fetch exam results with related data
    const examResults = await prisma.examResult.findMany({
      where: whereClause,
      include: {
        exam: {
          include: {
            class: { select: { id: true, name: true } },
            subject: { select: { id: true, name: true } },
            questions: {
              include: {
                question: {
                  select: { difficulty: true }
                }
              }
            }
          }
        },
        session: {
          include: {
            student: {
              select: {
                id: true,
                name: true,
                sid: true,
                className: true
              }
            }
          }
        }
      },
      orderBy: { submittedAt: 'desc' }
    })

    // Fetch all exams for overview
    const allExams = await prisma.exam.findMany({
      include: {
        class: { select: { id: true, name: true } },
        subject: { select: { id: true, name: true } },
        _count: {
          select: {
            results: true,
            sessions: true,
            questions: true
          }
        }
      }
    })

    // Calculate overview statistics
    const totalExams = allExams.length
    const activeExams = allExams.filter(exam => exam.isActive).length
    const totalResults = examResults.length
    const passedResults = examResults.filter(result => result.isPassed).length
    const averageScore = totalResults > 0 
      ? examResults.reduce((sum, result) => sum + result.percentage, 0) / totalResults 
      : 0
    const passRate = totalResults > 0 ? (passedResults / totalResults) * 100 : 0

    // Get unique students who took exams
    const uniqueStudents = new Set(examResults.map(result => result.session.student.id))
    const totalStudentsTested = uniqueStudents.size

    // Calculate subject-wise performance
    const subjectPerformance = examResults.reduce((acc: any, result) => {
      const subjectName = result.exam.subject.name
      if (!acc[subjectName]) {
        acc[subjectName] = {
          subjectName,
          totalResults: 0,
          totalScore: 0,
          passedCount: 0,
          averageScore: 0,
          passRate: 0
        }
      }
      acc[subjectName].totalResults++
      acc[subjectName].totalScore += result.percentage
      if (result.isPassed) acc[subjectName].passedCount++
      return acc
    }, {})

    // Calculate averages for subjects
    Object.values(subjectPerformance).forEach((subject: any) => {
      subject.averageScore = subject.totalScore / subject.totalResults
      subject.passRate = (subject.passedCount / subject.totalResults) * 100
    })

    // Calculate class-wise performance
    const classPerformance = examResults.reduce((acc: any, result) => {
      const className = result.exam.class.name
      if (!acc[className]) {
        acc[className] = {
          className,
          totalResults: 0,
          totalScore: 0,
          passedCount: 0,
          averageScore: 0,
          passRate: 0
        }
      }
      acc[className].totalResults++
      acc[className].totalScore += result.percentage
      if (result.isPassed) acc[className].passedCount++
      return acc
    }, {})

    // Calculate averages for classes
    Object.values(classPerformance).forEach((cls: any) => {
      cls.averageScore = cls.totalScore / cls.totalResults
      cls.passRate = (cls.passedCount / cls.totalResults) * 100
    })

    // Get top performers
    const topPerformers = examResults
      .sort((a, b) => b.percentage - a.percentage)
      .slice(0, 10)
      .map(result => ({
        studentName: result.session.student.name,
        studentId: result.session.student.sid,
        className: result.session.student.className,
        examTitle: result.exam.title,
        subject: result.exam.subject.name,
        percentage: result.percentage,
        marksObtained: result.marksObtained,
        totalMarks: result.totalMarks,
        submittedAt: result.submittedAt
      }))

    // Calculate difficulty analysis
    const difficultyStats = examResults.reduce((acc: any, result) => {
      result.exam.questions.forEach(examQuestion => {
        const difficulty = examQuestion.question.difficulty
        if (!acc[difficulty]) {
          acc[difficulty] = {
            difficulty,
            totalQuestions: 0,
            totalAttempts: 0,
            // Note: We'd need response data to calculate correct answers
          }
        }
        acc[difficulty].totalQuestions++
        acc[difficulty].totalAttempts++
      })
      return acc
    }, {})

    // Recent exam activity (last 7 days)
    const recentDate = new Date()
    recentDate.setDate(recentDate.getDate() - 7)
    
    const recentActivity = examResults
      .filter(result => new Date(result.submittedAt) >= recentDate)
      .map(result => ({
        id: result.id,
        studentName: result.session.student.name,
        examTitle: result.exam.title,
        subject: result.exam.subject.name,
        className: result.exam.class.name,
        percentage: result.percentage,
        isPassed: result.isPassed,
        submittedAt: result.submittedAt
      }))
      .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())

    // Performance trends (daily averages for the last 30 days)
    const performanceTrends = []
    for (let i = 29; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dayStart = new Date(date.setHours(0, 0, 0, 0))
      const dayEnd = new Date(date.setHours(23, 59, 59, 999))
      
      const dayResults = examResults.filter(result => {
        const submittedDate = new Date(result.submittedAt)
        return submittedDate >= dayStart && submittedDate <= dayEnd
      })
      
      const dayAverage = dayResults.length > 0 
        ? dayResults.reduce((sum, result) => sum + result.percentage, 0) / dayResults.length 
        : 0
      
      performanceTrends.push({
        date: dayStart.toISOString().split('T')[0],
        averageScore: Math.round(dayAverage * 100) / 100,
        totalExams: dayResults.length,
        passedExams: dayResults.filter(r => r.isPassed).length
      })
    }

    const analyticsData = {
      overview: {
        totalExams,
        activeExams,
        totalResults,
        totalStudentsTested,
        averageScore: Math.round(averageScore * 100) / 100,
        passRate: Math.round(passRate * 100) / 100,
        completionRate: totalResults > 0 ? Math.round((totalResults / (totalStudentsTested * totalExams)) * 100) : 0
      },
      performance: {
        subjectWise: Object.values(subjectPerformance),
        classWise: Object.values(classPerformance),
        topPerformers,
        recentActivity: recentActivity.slice(0, 20)
      },
      trends: {
        performanceTrends,
        difficultyAnalysis: Object.values(difficultyStats)
      },
      exams: allExams.map(exam => ({
        id: exam.id,
        title: exam.title,
        subject: exam.subject.name,
        class: exam.class.name,
        isActive: exam.isActive,
        totalResults: exam._count.results,
        totalSessions: exam._count.sessions,
        totalQuestions: exam._count.questions,
        createdAt: exam.createdAt
      }))
    }

    return NextResponse.json(analyticsData)

  } catch (error) {
    console.error('Error fetching exam analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exam analytics' },
      { status: 500 }
    )
  }
}
