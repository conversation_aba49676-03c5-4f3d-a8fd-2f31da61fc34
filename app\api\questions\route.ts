import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

export async function POST(request: Request) {
  try {
    // Verify authentication using cookies (like other dashboard endpoints)
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to create questions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      questionText,
      questionType,
      options,
      correctAnswer,
      marks,
      difficulty,
      explanation,
      subjectId
    } = body

    // Validate required fields
    if (!questionText || !questionType || !subjectId || !marks) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate question type specific requirements
    if (questionType === 'MULTIPLE_CHOICE') {
      if (!options || !Array.isArray(options) || options.length < 2) {
        return NextResponse.json(
          { error: 'Multiple choice questions must have at least 2 options' },
          { status: 400 }
        )
      }

      if (!correctAnswer) {
        return NextResponse.json(
          { error: 'Correct answer is required for multiple choice questions' },
          { status: 400 }
        )
      }

      // Validate that correct answer exists in options
      const optionKeys = options.map((opt: any) => opt.key)
      if (!optionKeys.includes(correctAnswer)) {
        return NextResponse.json(
          { error: 'Correct answer must match one of the option keys' },
          { status: 400 }
        )
      }
    }

    if (questionType === 'TRUE_FALSE') {
      if (!correctAnswer) {
        return NextResponse.json(
          { error: 'Correct answer is required for true/false questions' },
          { status: 400 }
        )
      }

      if (correctAnswer !== 'true' && correctAnswer !== 'false') {
        return NextResponse.json(
          { error: 'Correct answer for true/false questions must be "true" or "false"' },
          { status: 400 }
        )
      }
    }

    if (questionType === 'SHORT_ANSWER') {
      if (!correctAnswer || !correctAnswer.trim()) {
        return NextResponse.json(
          { error: 'Correct answer is required for short answer questions' },
          { status: 400 }
        )
      }
    }

    // Validate marks
    if (marks < 1 || marks > 10) {
      return NextResponse.json(
        { error: 'Marks must be between 1 and 10' },
        { status: 400 }
      )
    }

    // Verify subject exists and get classId
    const subject = await prisma.subject.findUnique({
      where: { id: subjectId },
      select: {
        id: true,
        name: true,
        classId: true
      }
    })

    if (!subject) {
      return NextResponse.json(
        { error: 'Subject not found' },
        { status: 404 }
      )
    }

    // Create the question
    const question = await prisma.question.create({
      data: {
        questionText: questionText.trim(),
        questionType,
        options: options || [],
        correctAnswer: correctAnswer || null,
        marks,
        difficulty,
        explanation: explanation?.trim() || null,
        subjectId,
        classId: subject.classId, // Include the classId from the subject
        createdBy: userData.id
      },
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    console.log(`Question created: ${question.id} by ${userData.email}`)

    return NextResponse.json({
      success: true,
      question: {
        id: question.id,
        questionText: question.questionText,
        questionType: question.questionType,
        options: question.options,
        marks: question.marks,
        difficulty: question.difficulty,
        subject: question.subject,
        createdAt: question.createdAt
      },
      message: 'Question created successfully'
    })

  } catch (error) {
    console.error('Error creating question:', error)
    return NextResponse.json(
      { error: 'Failed to create question' },
      { status: 500 }
    )
  }
}

export async function GET(request: Request) {
  try {
    // Verify authentication using cookies (like other dashboard endpoints)
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const subjectId = searchParams.get('subjectId')
    const difficulty = searchParams.get('difficulty')
    const questionType = searchParams.get('questionType')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {}

    if (subjectId) {
      where.subjectId = subjectId
    }

    if (difficulty) {
      where.difficulty = difficulty
    }

    if (questionType) {
      where.questionType = questionType
    }

    if (search) {
      where.questionText = {
        contains: search,
        mode: 'insensitive'
      }
    }

    // Get total count
    const totalCount = await prisma.question.count({ where })

    // Get questions with pagination
    const questions = await prisma.question.findMany({
      where,
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            examQuestions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      questions: questions.map(question => ({
        id: question.id,
        questionText: question.questionText,
        questionType: question.questionType,
        options: question.options,
        marks: question.marks,
        difficulty: question.difficulty,
        subject: question.subject,
        usageCount: question._count.examQuestions,
        createdAt: question.createdAt,
        updatedAt: question.updatedAt
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error fetching questions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch questions' },
      { status: 500 }
    )
  }
}
