import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to view live sessions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const examId = searchParams.get('examId')
    const status = searchParams.get('status') || 'IN_PROGRESS'

    // Build where clause for filtering
    const whereClause: any = {
      status: status
    }

    if (examId && examId !== 'all') {
      whereClause.examId = examId
    }

    // Fetch active exam sessions
    const liveSessions = await prisma.examSession.findMany({
      where: whereClause,
      include: {
        exam: {
          include: {
            class: { select: { id: true, name: true } },
            subject: { select: { id: true, name: true } },
            questions: {
              select: { id: true }
            }
          }
        },
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
            className: true
          }
        },
        responses: {
          select: {
            id: true,
            questionId: true,
            selectedAnswer: true,
            isCorrect: true,
            timeSpent: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            responses: true
          }
        }
      },
      orderBy: { startTime: 'desc' }
    })

    // Calculate session statistics
    const currentTime = new Date()
    const sessionsWithStats = liveSessions.map(session => {
      const timeElapsed = Math.floor((currentTime.getTime() - session.startTime.getTime()) / 1000)
      const timeRemaining = Math.max(0, (session.exam.duration * 60) - timeElapsed)
      const progressPercentage = session.exam.questions?.length 
        ? Math.round((session._count.responses / session.exam.questions.length) * 100)
        : 0

      // Calculate current score
      const correctResponses = session.responses.filter(r => r.isCorrect).length
      const currentScore = session.responses.length > 0 
        ? Math.round((correctResponses / session.responses.length) * 100)
        : 0

      // Determine session health
      let sessionHealth = 'good'
      if (timeRemaining < 300) { // Less than 5 minutes
        sessionHealth = 'warning'
      }
      if (timeRemaining === 0) {
        sessionHealth = 'expired'
      }
      if (session.warningCount > 2) {
        sessionHealth = 'suspicious'
      }

      // Calculate activity status
      const lastActivity = session.responses.length > 0 
        ? Math.max(...session.responses.map(r => new Date(r.createdAt).getTime()))
        : session.startTime.getTime()
      
      const timeSinceLastActivity = Math.floor((currentTime.getTime() - lastActivity) / 1000)
      let activityStatus = 'active'
      
      if (timeSinceLastActivity > 300) { // 5 minutes
        activityStatus = 'idle'
      }
      if (timeSinceLastActivity > 900) { // 15 minutes
        activityStatus = 'inactive'
      }

      return {
        id: session.id,
        student: {
          id: session.student.id,
          name: session.student.name,
          sid: session.student.sid,
          className: session.student.className
        },
        exam: {
          id: session.exam.id,
          title: session.exam.title,
          subject: session.exam.subject.name,
          class: session.exam.class.name,
          duration: session.exam.duration,
          totalQuestions: session.exam.questions?.length || 0
        },
        status: session.status,
        startTime: session.startTime,
        endTime: session.endTime,
        isSubmitted: session.isSubmitted,
        warningCount: session.warningCount,
        timeElapsed,
        timeRemaining,
        progressPercentage,
        questionsAnswered: session._count.responses,
        currentScore,
        sessionHealth,
        activityStatus,
        timeSinceLastActivity
      }
    })

    // Calculate overview statistics
    const totalActiveSessions = sessionsWithStats.filter(s => s.status === 'IN_PROGRESS').length
    const totalStudents = new Set(sessionsWithStats.map(s => s.student.id)).size
    const averageProgress = sessionsWithStats.length > 0 
      ? Math.round(sessionsWithStats.reduce((sum, s) => sum + s.progressPercentage, 0) / sessionsWithStats.length)
      : 0
    
    const suspiciousSessions = sessionsWithStats.filter(s => s.sessionHealth === 'suspicious').length
    const idleSessions = sessionsWithStats.filter(s => s.activityStatus === 'idle').length

    // Group sessions by exam
    const sessionsByExam = sessionsWithStats.reduce((acc: any, session) => {
      const examId = session.exam.id
      if (!acc[examId]) {
        acc[examId] = {
          examId,
          examTitle: session.exam.title,
          subject: session.exam.subject,
          class: session.exam.class,
          totalSessions: 0,
          activeSessions: 0,
          averageProgress: 0,
          sessions: []
        }
      }
      acc[examId].sessions.push(session)
      acc[examId].totalSessions++
      if (session.status === 'IN_PROGRESS') {
        acc[examId].activeSessions++
      }
      return acc
    }, {})

    // Calculate average progress for each exam
    Object.values(sessionsByExam).forEach((examGroup: any) => {
      examGroup.averageProgress = examGroup.sessions.length > 0
        ? Math.round(examGroup.sessions.reduce((sum: number, s: any) => sum + s.progressPercentage, 0) / examGroup.sessions.length)
        : 0
    })

    const liveSessionsData = {
      overview: {
        totalActiveSessions,
        totalStudents,
        averageProgress,
        suspiciousSessions,
        idleSessions,
        lastUpdated: currentTime.toISOString()
      },
      sessions: sessionsWithStats,
      sessionsByExam: Object.values(sessionsByExam)
    }

    return NextResponse.json(liveSessionsData)

  } catch (error) {
    console.error('Error fetching live sessions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch live sessions' },
      { status: 500 }
    )
  }
}

// POST - Administrative actions on sessions
export async function POST(request: Request) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to manage sessions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, sessionId, sessionIds, reason } = body

    switch (action) {
      case 'terminate_session':
        if (!sessionId) {
          return NextResponse.json(
            { error: 'Session ID is required' },
            { status: 400 }
          )
        }

        await prisma.examSession.update({
          where: { id: sessionId },
          data: {
            status: 'TERMINATED',
            endTime: new Date()
            // terminationReason: reason || 'Terminated by administrator' // Not in current schema
          }
        })

        console.log(`Session ${sessionId} terminated by ${userData.email}. Reason: ${reason}`)
        break

      case 'extend_time':
        if (!sessionId) {
          return NextResponse.json(
            { error: 'Session ID is required' },
            { status: 400 }
          )
        }

        const { additionalMinutes } = body
        if (!additionalMinutes || additionalMinutes <= 0) {
          return NextResponse.json(
            { error: 'Additional minutes must be a positive number' },
            { status: 400 }
          )
        }

        // Time extension not implemented in current schema
        // await prisma.examSession.update({
        //   where: { id: sessionId },
        //   data: {
        //     timeExtension: additionalMinutes
        //   }
        // })

        console.log(`Session ${sessionId} extended by ${additionalMinutes} minutes by ${userData.email}`)
        break

      case 'add_warning':
        if (!sessionId) {
          return NextResponse.json(
            { error: 'Session ID is required' },
            { status: 400 }
          )
        }

        await prisma.examSession.update({
          where: { id: sessionId },
          data: {
            warningCount: {
              increment: 1
            }
          }
        })

        console.log(`Warning added to session ${sessionId} by ${userData.email}. Reason: ${reason}`)
        break

      case 'bulk_terminate':
        if (!sessionIds || !Array.isArray(sessionIds)) {
          return NextResponse.json(
            { error: 'Session IDs array is required' },
            { status: 400 }
          )
        }

        await prisma.examSession.updateMany({
          where: {
            id: {
              in: sessionIds
            }
          },
          data: {
            status: 'TERMINATED',
            endTime: new Date()
            // terminationReason: reason || 'Bulk terminated by administrator' // Not in current schema
          }
        })

        console.log(`${sessionIds.length} sessions bulk terminated by ${userData.email}`)
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('Error performing session action:', error)
    return NextResponse.json(
      { error: 'Failed to perform action' },
      { status: 500 }
    )
  }
}
