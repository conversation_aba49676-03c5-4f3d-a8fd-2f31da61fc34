import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

export async function GET(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to view exam results
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { examId } = params

    // Fetch exam with results
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        class: { select: { id: true, name: true } },
        subject: { select: { id: true, name: true } },
        questions: {
          include: {
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                difficulty: true,
                options: true,
                correctAnswer: true
              }
            }
          },
          orderBy: { order: 'asc' }
        },
        results: {
          include: {
            session: {
              include: {
                student: {
                  select: {
                    id: true,
                    name: true,
                    sid: true,
                    className: true
                  }
                },
                responses: {
                  include: {
                    question: {
                      select: {
                        id: true,
                        questionText: true,
                        correctAnswer: true,
                        options: true
                      }
                    }
                  }
                }
              }
            }
          },
          orderBy: { submittedAt: 'desc' }
        }
      }
    })

    if (!exam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    // Calculate detailed statistics
    const totalResults = exam.results.length
    const passedResults = exam.results.filter(result => result.isPassed).length
    const averageScore = totalResults > 0 
      ? exam.results.reduce((sum, result) => sum + result.percentage, 0) / totalResults 
      : 0
    const passRate = totalResults > 0 ? (passedResults / totalResults) * 100 : 0

    // Calculate question-wise analysis
    const questionAnalysis = exam.questions.map(examQuestion => {
      const question = examQuestion.question
      const responses = exam.results.flatMap(result => 
        result.session.responses.filter(response => response.question.id === question.id)
      )
      
      const totalAttempts = responses.length
      const correctAttempts = responses.filter(response => response.isCorrect).length
      const successRate = totalAttempts > 0 ? (correctAttempts / totalAttempts) * 100 : 0
      
      // Calculate average time spent (if available)
      const averageTimeSpent = totalAttempts > 0 
        ? responses.reduce((sum, response) => sum + (response.timeSpent || 0), 0) / totalAttempts 
        : 0

      // Analyze wrong answers for multiple choice questions
      const wrongAnswers: { [key: string]: number } = {}
      if (question.questionType === 'MULTIPLE_CHOICE') {
        responses.filter(r => !r.isCorrect).forEach(response => {
          const answer = response.selectedAnswer
          wrongAnswers[answer] = (wrongAnswers[answer] || 0) + 1
        })
      }

      return {
        questionId: question.id,
        questionText: question.questionText,
        questionType: question.questionType,
        difficulty: question.difficulty,
        correctAnswer: question.correctAnswer,
        marks: examQuestion.marks,
        totalAttempts,
        correctAttempts,
        wrongAttempts: totalAttempts - correctAttempts,
        successRate: Math.round(successRate * 100) / 100,
        averageTimeSpent: Math.round(averageTimeSpent),
        commonWrongAnswers: Object.entries(wrongAnswers)
          .map(([answer, count]) => ({ answer, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 3)
      }
    })

    // Calculate grade distribution
    const gradeDistribution = exam.results.reduce((acc: { [key: string]: number }, result) => {
      const grade = getGrade(result.percentage)
      acc[grade] = (acc[grade] || 0) + 1
      return acc
    }, {})

    // Calculate time analysis
    const timeAnalysis = {
      averageTime: totalResults > 0 
        ? exam.results.reduce((sum, result) => sum + result.timeTaken, 0) / totalResults 
        : 0,
      minTime: totalResults > 0 ? Math.min(...exam.results.map(r => r.timeTaken)) : 0,
      maxTime: totalResults > 0 ? Math.max(...exam.results.map(r => r.timeTaken)) : 0,
      examDuration: exam.duration * 60 // Convert minutes to seconds
    }

    // Format results for response
    const formattedResults = exam.results.map(result => ({
      id: result.id,
      student: {
        id: result.session.student.id,
        name: result.session.student.name,
        sid: result.session.student.sid,
        className: result.session.student.className
      },
      marksObtained: result.marksObtained,
      totalMarks: result.totalMarks,
      percentage: result.percentage,
      grade: getGrade(result.percentage),
      isPassed: result.isPassed,
      correctAnswers: result.correctAnswers,
      wrongAnswers: result.wrongAnswers,
      totalQuestions: result.totalQuestions,
      attemptedQuestions: result.attemptedQuestions,
      timeTaken: result.timeTaken,
      submittedAt: result.submittedAt,
      responses: result.session.responses.map(response => ({
        questionId: response.question.id,
        questionText: response.question.questionText,
        selectedAnswer: response.selectedAnswer,
        correctAnswer: response.question.correctAnswer,
        isCorrect: response.isCorrect,
        timeSpent: response.timeSpent
      }))
    }))

    const examAnalysis = {
      exam: {
        id: exam.id,
        title: exam.title,
        description: exam.description,
        duration: exam.duration,
        totalMarks: exam.totalMarks,
        passingMarks: exam.passingMarks,
        class: exam.class,
        subject: exam.subject,
        totalQuestions: exam.questions.length,
        isActive: exam.isActive,
        createdAt: exam.createdAt
      },
      statistics: {
        totalResults,
        passedResults,
        failedResults: totalResults - passedResults,
        averageScore: Math.round(averageScore * 100) / 100,
        passRate: Math.round(passRate * 100) / 100,
        gradeDistribution,
        timeAnalysis
      },
      questionAnalysis,
      results: formattedResults
    }

    return NextResponse.json(examAnalysis)

  } catch (error) {
    console.error('Error fetching exam results:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exam results' },
      { status: 500 }
    )
  }
}

function getGrade(percentage: number): string {
  if (percentage >= 90) return 'A+'
  if (percentage >= 80) return 'A'
  if (percentage >= 70) return 'B+'
  if (percentage >= 60) return 'B'
  if (percentage >= 50) return 'C+'
  if (percentage >= 40) return 'C'
  if (percentage >= 33) return 'D'
  return 'F'
}
