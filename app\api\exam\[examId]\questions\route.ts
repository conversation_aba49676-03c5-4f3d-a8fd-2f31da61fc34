import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import { ExamQuestionsResponse } from '@/app/types/exam'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

export async function GET(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId } = studentData
    const { examId } = params
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    console.log(`Getting questions for exam ${examId}, session ${sessionId}`)

    // Verify session exists and belongs to student
    const session = await prisma.examSession.findFirst({
      where: {
        id: sessionId || '',
        examId: examId,
        studentId: studentId
      },
      include: {
        exam: {
          include: {
            questions: {
              include: {
                question: {
                  select: {
                    id: true,
                    questionText: true,
                    questionType: true,
                    options: true,
                    marks: true,
                    difficulty: true
                    // Note: We don't include correctAnswer for security
                  }
                }
              },
              orderBy: {
                order: 'asc'
              }
            }
          }
        }
      }
    })

    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session or exam' },
        { status: 404 }
      )
    }

    if (session.status !== 'IN_PROGRESS') {
      return NextResponse.json(
        { error: 'Exam session is not active' },
        { status: 400 }
      )
    }

    // Check if session has expired
    const currentTime = new Date()
    const timeElapsed = Math.floor((currentTime.getTime() - session.startTime.getTime()) / 1000)
    const timeRemaining = Math.max(0, (session.exam.duration * 60) - timeElapsed)

    if (timeRemaining <= 0) {
      // Session has expired, mark as expired
      await prisma.examSession.update({
        where: { id: session.id },
        data: { 
          status: 'EXPIRED',
          endTime: currentTime
        }
      })
      
      return NextResponse.json(
        { error: 'Exam session has expired' },
        { status: 400 }
      )
    }

    // Get student's existing responses for this session
    const responses = await prisma.examResponse.findMany({
      where: {
        sessionId: session.id
      },
      select: {
        questionId: true,
        selectedAnswer: true,
        isFlagged: true,
        timeSpent: true
      }
    })

    // Create response map for easy lookup
    const responseMap = responses.reduce((acc, response) => {
      acc[response.questionId] = {
        selectedAnswer: response.selectedAnswer,
        isFlagged: response.isFlagged,
        timeSpent: response.timeSpent
      }
      return acc
    }, {} as Record<string, any>)

    // Prepare questions with student responses
    const questions = session.exam.questions.map(examQuestion => {
      const question = examQuestion.question
      const studentResponse = responseMap[question.id]
      
      return {
        id: question.id,
        questionText: question.questionText,
        questionType: question.questionType,
        options: question.options,
        marks: examQuestion.marks,
        order: examQuestion.order,
        difficulty: question.difficulty,
        // Include student's response if exists
        selectedAnswer: studentResponse?.selectedAnswer || null,
        isFlagged: studentResponse?.isFlagged || false,
        timeSpent: studentResponse?.timeSpent || 0
      }
    })

    // Shuffle questions if exam setting is enabled
    let finalQuestions = questions
    if (session.exam.shuffleQuestions) {
      // For consistency, we should shuffle based on student ID + exam ID
      // This ensures the same student gets the same order on refresh
      const seed = `${studentId}-${examId}`
      finalQuestions = shuffleArray(questions, seed)
    }

    return NextResponse.json({
      questions: finalQuestions,
      shuffled: session.exam.shuffleQuestions,
      timeRemaining,
      sessionInfo: {
        id: session.id,
        startTime: session.startTime,
        warningCount: session.warningCount
      }
    })

  } catch (error) {
    console.error('Error fetching exam questions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exam questions' },
      { status: 500 }
    )
  }
}

// Simple deterministic shuffle function
function shuffleArray<T>(array: T[], seed: string): T[] {
  const shuffled = [...array]
  let hash = 0
  
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  
  // Use the hash as seed for shuffling
  for (let i = shuffled.length - 1; i > 0; i--) {
    hash = (hash * 9301 + 49297) % 233280
    const j = Math.floor((hash / 233280) * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  
  return shuffled
}
