const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestExamSessions() {
  try {
    console.log('🧪 Creating test exam sessions...')

    // First, let's check if we have any exams
    const exams = await prisma.exam.findMany({
      include: {
        class: true,
        subject: true
      }
    })

    if (exams.length === 0) {
      console.log('❌ No exams found. Creating a test exam first...')
      
      // Get a class and subject
      const classes = await prisma.class.findMany()
      const subjects = await prisma.subject.findMany()
      
      if (classes.length === 0 || subjects.length === 0) {
        console.log('❌ No classes or subjects found. Please create them first.')
        return
      }

      // Create a test exam
      const testExam = await prisma.exam.create({
        data: {
          title: 'Mathematics Test - Live Session Demo',
          description: 'A demo exam for testing live sessions',
          classId: classes[0].id,
          subjectId: subjects[0].id,
          duration: 60, // 60 minutes
          totalMarks: 100,
          passingMarks: 40,
          instructions: 'This is a demo exam for testing live sessions functionality.',
          isActive: true,
          createdBy: 'system'
        }
      })

      console.log(`✅ Created test exam: ${testExam.title}`)
      exams.push(testExam)
    }

    // Get some students
    const students = await prisma.student.findMany({
      take: 5 // Get first 5 students
    })

    if (students.length === 0) {
      console.log('❌ No students found. Please create some students first.')
      return
    }

    console.log(`📚 Found ${exams.length} exams and ${students.length} students`)

    // Create test exam sessions
    const testSessions = []
    const exam = exams[0] // Use the first exam

    for (let i = 0; i < Math.min(students.length, 3); i++) {
      const student = students[i]
      
      // Check if session already exists
      const existingSession = await prisma.examSession.findUnique({
        where: {
          examId_studentId: {
            examId: exam.id,
            studentId: student.id
          }
        }
      })

      if (existingSession) {
        console.log(`⚠️ Session already exists for student ${student.name}`)
        continue
      }

      // Create different session statuses for testing
      const statuses = ['IN_PROGRESS', 'IN_PROGRESS', 'COMPLETED']
      const status = statuses[i] || 'IN_PROGRESS'
      
      // Create start time (some started recently, some a while ago)
      const startTime = new Date()
      startTime.setMinutes(startTime.getMinutes() - (i * 15 + 10)) // Started 10, 25, 40 minutes ago

      const sessionData = {
        examId: exam.id,
        studentId: student.id,
        startTime: startTime,
        status: status,
        isSubmitted: status === 'COMPLETED',
        warningCount: i === 2 ? 2 : 0, // Give one student some warnings
        flaggedCount: 0
      }

      if (status === 'COMPLETED') {
        sessionData.endTime = new Date()
        sessionData.duration = 45 // Completed in 45 minutes
      }

      const session = await prisma.examSession.create({
        data: sessionData
      })

      testSessions.push(session)
      console.log(`✅ Created ${status} session for student: ${student.name}`)

      // Create some sample responses for active sessions
      if (status === 'IN_PROGRESS') {
        // Get some questions for this exam (if any exist)
        const examQuestions = await prisma.examQuestion.findMany({
          where: { examId: exam.id },
          include: { question: true },
          take: 3
        })

        for (let j = 0; j < examQuestions.length; j++) {
          const examQuestion = examQuestions[j]
          const isCorrect = Math.random() > 0.3 // 70% chance of correct answer
          
          await prisma.examResponse.create({
            data: {
              sessionId: session.id,
              questionId: examQuestion.question.id,
              selectedAnswer: isCorrect ? examQuestion.question.correctAnswer : 'A',
              isCorrect: isCorrect,
              marks: isCorrect ? examQuestion.marks : 0,
              timeSpent: Math.floor(Math.random() * 120) + 30, // 30-150 seconds
              isVisited: true
            }
          })
        }

        console.log(`   📝 Added ${examQuestions.length} responses for ${student.name}`)
      }
    }

    console.log(`\n🎉 Successfully created ${testSessions.length} test exam sessions!`)
    console.log('\n📊 Session Summary:')
    
    const sessionCounts = await prisma.examSession.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    sessionCounts.forEach(count => {
      console.log(`   ${count.status}: ${count._count.status} sessions`)
    })

    console.log('\n🚀 You can now test the Live Sessions page!')
    console.log('   Navigate to: http://localhost:3001/dashboard/online-exam/live-sessions')

  } catch (error) {
    console.error('❌ Error creating test exam sessions:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
createTestExamSessions()
