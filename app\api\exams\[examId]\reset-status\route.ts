import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// POST - Reset exam status to make it active and available
export async function POST(
  request: Request,
  { params }: { params: { examId: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check permissions - only SUPER_ADMIN and ADMIN can reset exam status
    if (!['SUPER_ADMIN', 'ADMIN'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to reset exam status' },
        { status: 403 }
      )
    }

    const { examId } = params

    // Check if exam exists
    const existingExam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        sessions: {
          select: {
            id: true,
            status: true,
            isSubmitted: true,
            studentId: true
          }
        }
      }
    })

    if (!existingExam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    // Reset exam to active state
    const currentTime = new Date()
    const futureTime = new Date(currentTime.getTime() + (7 * 24 * 60 * 60 * 1000)) // 7 days from now

    const updatedExam = await prisma.$transaction(async (tx) => {
      // 1. Update exam to be active with no time restrictions
      const exam = await tx.exam.update({
        where: { id: examId },
        data: {
          isActive: true,
          startTime: null, // Remove start time restriction
          endTime: null,   // Remove end time restriction
          updatedAt: currentTime
        }
      })

      // 2. Reset any expired sessions to allow retakes
      await tx.examSession.updateMany({
        where: {
          examId: examId,
          status: 'EXPIRED'
        },
        data: {
          status: 'COMPLETED', // Change from EXPIRED to COMPLETED so students can retake if allowed
          endTime: currentTime
        }
      })

      // 3. If exam doesn't allow retakes, enable retakes temporarily
      if (!exam.allowRetake) {
        await tx.exam.update({
          where: { id: examId },
          data: {
            allowRetake: true
          }
        })
      }

      return exam
    })

    console.log(`Successfully reset exam status: ${updatedExam.title}`)
    
    return NextResponse.json({ 
      message: 'Exam status reset successfully',
      exam: {
        id: updatedExam.id,
        title: updatedExam.title,
        isActive: updatedExam.isActive,
        startTime: updatedExam.startTime,
        endTime: updatedExam.endTime,
        allowRetake: updatedExam.allowRetake
      },
      changes: {
        madeActive: true,
        removedTimeRestrictions: true,
        enabledRetakes: !existingExam.allowRetake,
        resetExpiredSessions: existingExam.sessions.filter(s => s.status === 'EXPIRED').length
      }
    })

  } catch (error) {
    console.error('Error resetting exam status:', error)
    return NextResponse.json(
      { error: 'Failed to reset exam status' },
      { status: 500 }
    )
  }
}
