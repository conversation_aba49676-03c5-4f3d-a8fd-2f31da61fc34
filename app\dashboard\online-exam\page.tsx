'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { 
  BookOpen, 
  Users, 
  Clock, 
  TrendingUp, 
  Plus, 
  Eye,
  FileText,
  BarChart3,
  Monitor,
  CheckCircle,
  AlertCircle,
  Calendar
} from 'lucide-react'
import Link from 'next/link'

interface ExamStats {
  totalExams: number
  activeExams: number
  totalStudents: number
  averageScore: number
  completionRate: number
  recentExams: Array<{
    id: string
    title: string
    subject: string
    class: string
    studentsAttempted: number
    totalStudents: number
    averageScore: number
    status: 'active' | 'completed' | 'scheduled'
    createdAt: string
  }>
  upcomingExams: Array<{
    id: string
    title: string
    subject: string
    class: string
    startTime: string
    duration: number
  }>
}

export default function OnlineExamDashboard() {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'semester'>('month')

  // Fetch exam statistics
  const { data: stats, isLoading, error } = useQuery<ExamStats>({
    queryKey: ['exam-stats', selectedPeriod],
    queryFn: async () => {
      const response = await fetch(`/api/exam-stats?period=${selectedPeriod}`)
      if (!response.ok) {
        throw new Error('Failed to fetch exam statistics')
      }
      return response.json()
    }
  })

  // Mock data for development
  const mockStats: ExamStats = {
    totalExams: 24,
    activeExams: 3,
    totalStudents: 450,
    averageScore: 78.5,
    completionRate: 92.3,
    recentExams: [
      {
        id: '1',
        title: 'Mathematics Mid-term',
        subject: 'Mathematics',
        class: 'Grade 10',
        studentsAttempted: 45,
        totalStudents: 48,
        averageScore: 82.3,
        status: 'completed',
        createdAt: '2024-01-15'
      },
      {
        id: '2',
        title: 'Science Quiz',
        subject: 'Science',
        class: 'Grade 9',
        studentsAttempted: 38,
        totalStudents: 42,
        averageScore: 75.8,
        status: 'active',
        createdAt: '2024-01-14'
      },
      {
        id: '3',
        title: 'English Literature',
        subject: 'English',
        class: 'Grade 11',
        studentsAttempted: 0,
        totalStudents: 35,
        averageScore: 0,
        status: 'scheduled',
        createdAt: '2024-01-16'
      }
    ],
    upcomingExams: [
      {
        id: '4',
        title: 'History Final',
        subject: 'History',
        class: 'Grade 12',
        startTime: '2024-01-20T09:00:00Z',
        duration: 120
      },
      {
        id: '5',
        title: 'Chemistry Lab Test',
        subject: 'Chemistry',
        class: 'Grade 11',
        startTime: '2024-01-22T14:00:00Z',
        duration: 90
      }
    ]
  }

  const displayStats = stats || mockStats

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Online Exam Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage and monitor online examinations
            </p>
          </div>
          
          <div className="flex space-x-3">
            <Link href="/dashboard/online-exam/questions/create">
              <Button variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Question
              </Button>
            </Link>
            <Link href="/dashboard/online-exam/exams/create">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Exam
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{displayStats.totalExams}</div>
              <p className="text-xs text-muted-foreground">
                {displayStats.activeExams} currently active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{displayStats.totalStudents}</div>
              <p className="text-xs text-muted-foreground">
                Registered for exams
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{displayStats.averageScore}%</div>
              <p className="text-xs text-muted-foreground">
                Across all exams
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{displayStats.completionRate}%</div>
              <p className="text-xs text-muted-foreground">
                Students completing exams
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/dashboard/online-exam/exams">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="flex items-center p-6">
                <FileText className="h-8 w-8 text-blue-600 mr-4" />
                <div>
                  <h3 className="font-semibold">Manage Exams</h3>
                  <p className="text-sm text-gray-600">Create and edit exams</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/online-exam/questions">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="flex items-center p-6">
                <BookOpen className="h-8 w-8 text-green-600 mr-4" />
                <div>
                  <h3 className="font-semibold">Question Bank</h3>
                  <p className="text-sm text-gray-600">Manage questions</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/online-exam/results">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="flex items-center p-6">
                <BarChart3 className="h-8 w-8 text-purple-600 mr-4" />
                <div>
                  <h3 className="font-semibold">Results</h3>
                  <p className="text-sm text-gray-600">View analytics</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/online-exam/live-sessions">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="flex items-center p-6">
                <Monitor className="h-8 w-8 text-orange-600 mr-4" />
                <div>
                  <h3 className="font-semibold">Live Sessions</h3>
                  <p className="text-sm text-gray-600">Monitor active exams</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Exams */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Exams</CardTitle>
              <CardDescription>Latest exam activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {displayStats.recentExams.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{exam.title}</h4>
                      <p className="text-sm text-gray-600">
                        {exam.subject} • {exam.class}
                      </p>
                      <p className="text-xs text-gray-500">
                        {exam.studentsAttempted}/{exam.totalStudents} students
                        {exam.averageScore > 0 && ` • Avg: ${exam.averageScore}%`}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(exam.status)}>
                        {exam.status}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Exams */}
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Exams</CardTitle>
              <CardDescription>Scheduled examinations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {displayStats.upcomingExams.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{exam.title}</h4>
                      <p className="text-sm text-gray-600">
                        {exam.subject} • {exam.class}
                      </p>
                      <p className="text-xs text-gray-500 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDateTime(exam.startTime)}
                        <Clock className="h-3 w-3 ml-2 mr-1" />
                        {exam.duration} min
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
