import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import { AvailableExamsResponse } from '@/app/types/exam'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

export async function GET(request: Request) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId, className } = studentData

    console.log(`Fetching available exams for student: ${studentId}, class: ${className}`)

    // Get student's class ID
    const studentClass = await prisma.class.findUnique({
      where: { name: className },
      select: { id: true }
    })

    if (!studentClass) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    const currentTime = new Date()

    // Fetch available exams for the student's class
    const exams = await prisma.exam.findMany({
      where: {
        classId: studentClass.id,
        isActive: true,
        AND: [
          {
            OR: [
              { startTime: null }, // No start time restriction
              { startTime: { lte: currentTime } } // Start time has passed
            ]
          },
          {
            OR: [
              { endTime: null }, // No end time restriction
              { endTime: { gte: currentTime } } // End time hasn't passed
            ]
          }
        ]
      },
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        questions: {
          select: {
            id: true
          }
        },
        sessions: {
          where: {
            studentId: studentId
          },
          select: {
            id: true,
            status: true,
            isSubmitted: true,
            createdAt: true
          }
        }
      },
      orderBy: [
        { startTime: 'asc' },
        { createdAt: 'desc' }
      ]
    })

    // Process exams to add availability status
    const processedExams = exams.map(exam => {
      const existingSession = exam.sessions[0] // Student can have only one session per exam
      
      let status = 'available'
      let canStart = true
      let message = ''

      // Check if student has already taken the exam
      if (existingSession) {
        if (existingSession.isSubmitted) {
          status = 'completed'
          canStart = false
          message = 'You have already completed this exam'
        } else if (existingSession.status === 'IN_PROGRESS') {
          status = 'in_progress'
          canStart = true
          message = 'You can continue this exam'
        } else if (existingSession.status === 'EXPIRED') {
          status = 'expired'
          canStart = false
          message = 'This exam session has expired'
        }
      }

      // Check if retake is allowed
      if (existingSession && existingSession.isSubmitted && !exam.allowRetake) {
        canStart = false
      }

      // Check time restrictions
      if (exam.startTime && exam.startTime > currentTime) {
        status = 'not_started'
        canStart = false
        message = `Exam will be available from ${exam.startTime.toLocaleString()}`
      }

      if (exam.endTime && exam.endTime < currentTime) {
        status = 'ended'
        canStart = false
        message = 'Exam period has ended'
      }

      return {
        id: exam.id,
        title: exam.title,
        description: exam.description,
        duration: exam.duration,
        totalMarks: exam.totalMarks,
        passingMarks: exam.passingMarks,
        instructions: exam.instructions,
        startTime: exam.startTime,
        endTime: exam.endTime,
        allowRetake: exam.allowRetake,
        showResults: exam.showResults,
        createdAt: exam.createdAt,
        updatedAt: exam.updatedAt,
        class: exam.class,
        subject: exam.subject,
        questionCount: exam.questions.length,
        status,
        canStart,
        message,
        existingSessionId: existingSession?.id
      }
    })

    console.log(`Found ${processedExams.length} exams for student`)

    return NextResponse.json({
      exams: processedExams
    } as AvailableExamsResponse)

  } catch (error) {
    console.error('Error fetching available exams:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available exams' },
      { status: 500 }
    )
  }
}
