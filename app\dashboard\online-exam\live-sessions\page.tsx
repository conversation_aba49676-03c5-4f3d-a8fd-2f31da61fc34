'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Badge } from '@/app/components/ui/badge'
import { Progress } from '@/app/components/ui/progress'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { 
  Activity,
  Users,
  Clock,
  AlertTriangle,
  Eye,
  StopCircle,
  Plus,
  RefreshCw,
  Search,
  Filter,
  Download,
  Settings,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Timer,
  Target,
  Zap,
  UserX,
  Shield
} from 'lucide-react'

interface LiveSession {
  id: string
  student: {
    id: string
    name: string
    sid: string
    email: string
    className: string
  }
  exam: {
    id: string
    title: string
    subject: string
    class: string
    duration: number
    totalQuestions: number
  }
  status: string
  startTime: string
  endTime?: string
  isSubmitted: boolean
  warningCount: number
  timeElapsed: number
  timeRemaining: number
  progressPercentage: number
  questionsAnswered: number
  currentScore: number
  sessionHealth: 'good' | 'warning' | 'expired' | 'suspicious'
  activityStatus: 'active' | 'idle' | 'inactive'
  timeSinceLastActivity: number
}

interface LiveSessionsData {
  overview: {
    totalActiveSessions: number
    totalStudents: number
    averageProgress: number
    suspiciousSessions: number
    idleSessions: number
    lastUpdated: string
  }
  sessions: LiveSession[]
  sessionsByExam: Array<{
    examId: string
    examTitle: string
    subject: string
    class: string
    totalSessions: number
    activeSessions: number
    averageProgress: number
    sessions: LiveSession[]
  }>
}

export default function LiveSessionsPage() {
  const [filters, setFilters] = useState({
    examId: 'all',
    status: 'IN_PROGRESS'
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSessions, setSelectedSessions] = useState<string[]>([])
  const [autoRefresh, setAutoRefresh] = useState(true)

  const queryClient = useQueryClient()

  // Fetch live sessions data
  const { data: liveSessionsData, isLoading, error, refetch } = useQuery({
    queryKey: ['live-sessions', filters],
    queryFn: async (): Promise<LiveSessionsData> => {
      const params = new URLSearchParams()
      if (filters.examId !== 'all') params.append('examId', filters.examId)
      params.append('status', filters.status)

      const response = await fetch(`/api/live-sessions?${params.toString()}`)
      if (!response.ok) throw new Error('Failed to fetch live sessions')
      return response.json()
    },
    refetchInterval: autoRefresh ? 30000 : false, // Auto-refresh every 30 seconds
    staleTime: 10000, // 10 seconds
  })

  // Fetch exams for filter
  const { data: exams } = useQuery({
    queryKey: ['exams'],
    queryFn: async () => {
      const response = await fetch('/api/exams')
      if (!response.ok) throw new Error('Failed to fetch exams')
      return response.json()
    }
  })

  // Session action mutation
  const sessionActionMutation = useMutation({
    mutationFn: async ({ action, sessionId, sessionIds, reason, additionalMinutes }: any) => {
      const response = await fetch('/api/live-sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ action, sessionId, sessionIds, reason, additionalMinutes })
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to perform action')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['live-sessions'] })
      setSelectedSessions([])
    },
    onError: (error: any) => {
      alert(error.message || 'Failed to perform action')
    }
  })

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleSessionAction = (action: string, sessionId?: string, additionalData?: any) => {
    let reason = ''
    
    if (action === 'terminate_session') {
      reason = prompt('Reason for termination (optional):') || ''
      if (reason === null) return // User cancelled
    } else if (action === 'add_warning') {
      reason = prompt('Warning reason:') || ''
      if (!reason) {
        alert('Warning reason is required')
        return
      }
    } else if (action === 'extend_time') {
      const minutes = prompt('Additional minutes:')
      if (!minutes || isNaN(Number(minutes))) {
        alert('Please enter a valid number of minutes')
        return
      }
      additionalData = { additionalMinutes: Number(minutes) }
    }

    sessionActionMutation.mutate({
      action,
      sessionId,
      sessionIds: action === 'bulk_terminate' ? selectedSessions : undefined,
      reason,
      ...additionalData
    })
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'good': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'expired': return 'text-red-600 bg-red-100'
      case 'suspicious': return 'text-purple-600 bg-purple-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getActivityColor = (activity: string) => {
    switch (activity) {
      case 'active': return 'text-green-600 bg-green-100'
      case 'idle': return 'text-yellow-600 bg-yellow-100'
      case 'inactive': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Filter sessions based on search term
  const filteredSessions = liveSessionsData?.sessions.filter(session =>
    session.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    session.student.sid.toLowerCase().includes(searchTerm.toLowerCase()) ||
    session.exam.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading live sessions...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <Alert>
            <AlertDescription>
              Failed to load live sessions. Please try again.
            </AlertDescription>
          </Alert>
          <Button onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Live Sessions
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Real-time monitoring of active exam sessions
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Last updated: {liveSessionsData?.overview.lastUpdated ? formatDateTime(liveSessionsData.overview.lastUpdated) : 'Never'}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={autoRefresh ? "default" : "outline"}
              onClick={() => setAutoRefresh(!autoRefresh)}
              size="sm"
            >
              {autoRefresh ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              Auto Refresh
            </Button>
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-700 text-sm font-medium">Active Sessions</p>
                  <p className="text-2xl font-bold text-blue-900 mt-1">
                    {liveSessionsData?.overview.totalActiveSessions || 0}
                  </p>
                </div>
                <Activity className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-700 text-sm font-medium">Students Online</p>
                  <p className="text-2xl font-bold text-green-900 mt-1">
                    {liveSessionsData?.overview.totalStudents || 0}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-700 text-sm font-medium">Avg Progress</p>
                  <p className="text-2xl font-bold text-purple-900 mt-1">
                    {liveSessionsData?.overview.averageProgress || 0}%
                  </p>
                </div>
                <Target className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-yellow-100">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-700 text-sm font-medium">Idle Sessions</p>
                  <p className="text-2xl font-bold text-yellow-900 mt-1">
                    {liveSessionsData?.overview.idleSessions || 0}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-red-100">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-700 text-sm font-medium">Suspicious</p>
                  <p className="text-2xl font-bold text-red-900 mt-1">
                    {liveSessionsData?.overview.suspiciousSessions || 0}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters & Controls</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search students or exams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>

              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="EXPIRED">Expired</SelectItem>
                  <SelectItem value="TERMINATED">Terminated</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.examId} onValueChange={(value) => handleFilterChange('examId', value)}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="All exams" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Exams</SelectItem>
                  {exams?.exams?.map((exam: any) => (
                    <SelectItem key={exam.id} value={exam.id}>
                      {exam.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedSessions.length > 0 && (
                <div className="flex items-center space-x-2 ml-auto">
                  <Badge variant="outline">
                    {selectedSessions.length} selected
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSessionAction('bulk_terminate')}
                    className="text-red-600 hover:text-red-700"
                  >
                    <StopCircle className="h-4 w-4 mr-2" />
                    Terminate Selected
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <Tabs defaultValue="sessions" className="space-y-6">
          <TabsList>
            <TabsTrigger value="sessions">All Sessions</TabsTrigger>
            <TabsTrigger value="by-exam">By Exam</TabsTrigger>
            <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          </TabsList>

          {/* All Sessions Tab */}
          <TabsContent value="sessions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Live Exam Sessions</CardTitle>
                <CardDescription>
                  Real-time view of all active exam sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredSessions.map((session) => (
                    <div key={session.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <input
                            type="checkbox"
                            checked={selectedSessions.includes(session.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedSessions([...selectedSessions, session.id])
                              } else {
                                setSelectedSessions(selectedSessions.filter(id => id !== session.id))
                              }
                            }}
                            className="h-4 w-4"
                          />

                          <div className={`p-3 rounded-full ${
                            session.activityStatus === 'active' ? 'bg-green-100' :
                            session.activityStatus === 'idle' ? 'bg-yellow-100' : 'bg-red-100'
                          }`}>
                            {session.activityStatus === 'active' ? (
                              <Zap className="h-5 w-5 text-green-600" />
                            ) : session.activityStatus === 'idle' ? (
                              <Clock className="h-5 w-5 text-yellow-600" />
                            ) : (
                              <UserX className="h-5 w-5 text-red-600" />
                            )}
                          </div>

                          <div>
                            <h4 className="font-medium">{session.student.name}</h4>
                            <p className="text-sm text-gray-600">ID: {session.student.sid}</p>
                            <p className="text-xs text-gray-500">{session.student.className}</p>
                          </div>

                          <div className="hidden md:block">
                            <p className="font-medium text-sm">{session.exam.title}</p>
                            <p className="text-xs text-gray-600">{session.exam.subject}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-6">
                          <div className="text-center">
                            <p className="text-xs text-gray-600">Progress</p>
                            <div className="flex items-center space-x-2">
                              <Progress value={session.progressPercentage} className="w-16 h-2" />
                              <span className="text-sm font-medium">{session.progressPercentage}%</span>
                            </div>
                            <p className="text-xs text-gray-500">
                              {session.questionsAnswered}/{session.exam.totalQuestions}
                            </p>
                          </div>

                          <div className="text-center">
                            <p className="text-xs text-gray-600">Time Left</p>
                            <p className={`font-medium ${
                              session.timeRemaining < 300 ? 'text-red-600' :
                              session.timeRemaining < 900 ? 'text-yellow-600' : 'text-green-600'
                            }`}>
                              {formatTime(session.timeRemaining)}
                            </p>
                            <p className="text-xs text-gray-500">
                              of {session.exam.duration}m
                            </p>
                          </div>

                          <div className="text-center">
                            <p className="text-xs text-gray-600">Score</p>
                            <p className="font-medium">{session.currentScore}%</p>
                            {session.warningCount > 0 && (
                              <Badge variant="outline" className="text-red-600 text-xs">
                                {session.warningCount} warnings
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center space-x-2">
                            <Badge className={getHealthColor(session.sessionHealth)}>
                              {session.sessionHealth}
                            </Badge>
                            <Badge className={getActivityColor(session.activityStatus)}>
                              {session.activityStatus}
                            </Badge>
                          </div>

                          <div className="flex items-center space-x-1">
                            <Button variant="outline" size="sm" title="View Details">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSessionAction('extend_time', session.id)}
                              title="Extend Time"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSessionAction('add_warning', session.id)}
                              title="Add Warning"
                              className="text-yellow-600"
                            >
                              <AlertTriangle className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSessionAction('terminate_session', session.id)}
                              title="Terminate Session"
                              className="text-red-600"
                            >
                              <StopCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {filteredSessions.length === 0 && (
                    <div className="text-center py-12">
                      <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Sessions</h3>
                      <p className="text-gray-600">
                        {filters.status === 'IN_PROGRESS'
                          ? 'No students are currently taking exams.'
                          : `No ${filters.status.toLowerCase()} sessions found.`
                        }
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* By Exam Tab */}
          <TabsContent value="by-exam" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {liveSessionsData?.sessionsByExam?.map((examGroup) => (
                <Card key={examGroup.examId}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{examGroup.examTitle}</CardTitle>
                        <CardDescription>
                          {examGroup.subject} • {examGroup.class}
                        </CardDescription>
                      </div>
                      <Badge variant="outline">
                        {examGroup.activeSessions} active
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <p className="text-2xl font-bold text-blue-600">{examGroup.totalSessions}</p>
                          <p className="text-xs text-gray-600">Total Sessions</p>
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-green-600">{examGroup.activeSessions}</p>
                          <p className="text-xs text-gray-600">Active Now</p>
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-purple-600">{examGroup.averageProgress}%</p>
                          <p className="text-xs text-gray-600">Avg Progress</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {examGroup.sessions.slice(0, 3).map((session) => (
                          <div key={session.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div>
                              <p className="text-sm font-medium">{session.student.name}</p>
                              <p className="text-xs text-gray-600">{session.student.sid}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Progress value={session.progressPercentage} className="w-12 h-2" />
                              <span className="text-xs">{session.progressPercentage}%</span>
                              <Badge className={getActivityColor(session.activityStatus)} size="sm">
                                {session.activityStatus}
                              </Badge>
                            </div>
                          </div>
                        ))}

                        {examGroup.sessions.length > 3 && (
                          <p className="text-xs text-gray-500 text-center">
                            +{examGroup.sessions.length - 3} more sessions
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {(!liveSessionsData?.sessionsByExam || liveSessionsData.sessionsByExam.length === 0) && (
                <div className="col-span-2 text-center py-12">
                  <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Exam Sessions</h3>
                  <p className="text-gray-600">No active exam sessions to display.</p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Monitoring Tab */}
          <TabsContent value="monitoring" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Suspicious Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-red-600">
                    <Shield className="h-5 w-5" />
                    <span>Suspicious Activity</span>
                  </CardTitle>
                  <CardDescription>Sessions requiring attention</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {filteredSessions
                      .filter(session => session.sessionHealth === 'suspicious' || session.warningCount > 0)
                      .map((session) => (
                        <div key={session.id} className="p-3 border border-red-200 bg-red-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-red-900">{session.student.name}</p>
                              <p className="text-sm text-red-700">{session.exam.title}</p>
                              <p className="text-xs text-red-600">
                                {session.warningCount} warnings • {session.sessionHealth}
                              </p>
                            </div>
                            <div className="flex space-x-1">
                              <Button variant="outline" size="sm" className="text-red-600">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleSessionAction('terminate_session', session.id)}
                                className="text-red-600"
                              >
                                <StopCircle className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}

                    {filteredSessions.filter(s => s.sessionHealth === 'suspicious' || s.warningCount > 0).length === 0 && (
                      <div className="text-center py-8">
                        <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <p className="text-green-600 font-medium">All Clear</p>
                        <p className="text-sm text-gray-600">No suspicious activity detected</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Idle Sessions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-yellow-600">
                    <Clock className="h-5 w-5" />
                    <span>Idle Sessions</span>
                  </CardTitle>
                  <CardDescription>Students who may need assistance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {filteredSessions
                      .filter(session => session.activityStatus === 'idle' || session.activityStatus === 'inactive')
                      .map((session) => (
                        <div key={session.id} className="p-3 border border-yellow-200 bg-yellow-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-yellow-900">{session.student.name}</p>
                              <p className="text-sm text-yellow-700">{session.exam.title}</p>
                              <p className="text-xs text-yellow-600">
                                Idle for {formatTime(session.timeSinceLastActivity)}
                              </p>
                            </div>
                            <div className="flex space-x-1">
                              <Button variant="outline" size="sm" className="text-yellow-600">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleSessionAction('add_warning', session.id)}
                                className="text-yellow-600"
                              >
                                <AlertTriangle className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}

                    {filteredSessions.filter(s => s.activityStatus === 'idle' || s.activityStatus === 'inactive').length === 0 && (
                      <div className="text-center py-8">
                        <Activity className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <p className="text-green-600 font-medium">All Active</p>
                        <p className="text-sm text-gray-600">All students are actively taking exams</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Session Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Session Timeline</CardTitle>
                <CardDescription>Recent session activity and events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredSessions
                    .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
                    .slice(0, 10)
                    .map((session) => (
                      <div key={session.id} className="flex items-center space-x-4 p-3 border-l-4 border-blue-200 bg-blue-50 rounded-r-lg">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <Timer className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            {session.student.name} started {session.exam.title}
                          </p>
                          <p className="text-xs text-gray-600">
                            {formatDateTime(session.startTime)} • {formatTime(session.timeElapsed)} ago
                          </p>
                        </div>
                        <Badge className={getActivityColor(session.activityStatus)}>
                          {session.activityStatus}
                        </Badge>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
