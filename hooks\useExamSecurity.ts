'use client'

import { useEffect, useRef, useState } from 'react'

interface SecurityEvent {
  type: 'tab_switch' | 'window_blur' | 'fullscreen_exit' | 'copy_attempt' | 'paste_attempt' | 'right_click'
  timestamp: number
  details?: string
}

interface ExamSecurityConfig {
  maxTabSwitches?: number
  maxWindowBlurs?: number
  enforceFullscreen?: boolean
  disableRightClick?: boolean
  disableCopyPaste?: boolean
  disableDevTools?: boolean
  onSecurityViolation?: (event: SecurityEvent) => void
  onWarningThreshold?: (violationCount: number) => void
}

export function useExamSecurity(config: ExamSecurityConfig = {}) {
  const {
    maxTabSwitches = 3,
    maxWindowBlurs = 5,
    enforceFullscreen = true,
    disableRightClick = true,
    disableCopyPaste = true,
    disableDevTools = true,
    onSecurityViolation,
    onWarningThreshold
  } = config

  const [violations, setViolations] = useState<SecurityEvent[]>([])
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [warningCount, setWarningCount] = useState(0)
  
  const tabSwitchCount = useRef(0)
  const windowBlurCount = useRef(0)
  const lastActiveTime = useRef(Date.now())

  const logViolation = (event: SecurityEvent) => {
    setViolations(prev => [...prev, event])
    onSecurityViolation?.(event)
    
    // Check if warning threshold is reached
    const newWarningCount = warningCount + 1
    setWarningCount(newWarningCount)
    
    if (newWarningCount >= 3) {
      onWarningThreshold?.(newWarningCount)
    }
  }

  // Visibility change detection (tab switching)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        tabSwitchCount.current += 1
        logViolation({
          type: 'tab_switch',
          timestamp: Date.now(),
          details: `Tab switch #${tabSwitchCount.current}`
        })

        if (tabSwitchCount.current >= maxTabSwitches) {
          // Could trigger auto-submit or warning
          console.warn('Maximum tab switches exceeded')
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [maxTabSwitches])

  // Window blur detection
  useEffect(() => {
    const handleWindowBlur = () => {
      windowBlurCount.current += 1
      logViolation({
        type: 'window_blur',
        timestamp: Date.now(),
        details: `Window blur #${windowBlurCount.current}`
      })

      if (windowBlurCount.current >= maxWindowBlurs) {
        console.warn('Maximum window blurs exceeded')
      }
    }

    const handleWindowFocus = () => {
      lastActiveTime.current = Date.now()
    }

    window.addEventListener('blur', handleWindowBlur)
    window.addEventListener('focus', handleWindowFocus)
    
    return () => {
      window.removeEventListener('blur', handleWindowBlur)
      window.removeEventListener('focus', handleWindowFocus)
    }
  }, [maxWindowBlurs])

  // Fullscreen monitoring
  useEffect(() => {
    if (!enforceFullscreen) return

    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!document.fullscreenElement
      setIsFullscreen(isCurrentlyFullscreen)
      
      if (!isCurrentlyFullscreen && violations.length > 0) {
        logViolation({
          type: 'fullscreen_exit',
          timestamp: Date.now(),
          details: 'User exited fullscreen mode'
        })
      }
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [enforceFullscreen, violations.length])

  // Disable right-click
  useEffect(() => {
    if (!disableRightClick) return

    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault()
      logViolation({
        type: 'right_click',
        timestamp: Date.now(),
        details: 'Right-click attempted'
      })
    }

    document.addEventListener('contextmenu', handleContextMenu)
    return () => document.removeEventListener('contextmenu', handleContextMenu)
  }, [disableRightClick])

  // Disable copy/paste
  useEffect(() => {
    if (!disableCopyPaste) return

    const handleCopy = (e: ClipboardEvent) => {
      e.preventDefault()
      logViolation({
        type: 'copy_attempt',
        timestamp: Date.now(),
        details: 'Copy attempt blocked'
      })
    }

    const handlePaste = (e: ClipboardEvent) => {
      e.preventDefault()
      logViolation({
        type: 'paste_attempt',
        timestamp: Date.now(),
        details: 'Paste attempt blocked'
      })
    }

    document.addEventListener('copy', handleCopy)
    document.addEventListener('paste', handlePaste)
    
    return () => {
      document.removeEventListener('copy', handleCopy)
      document.removeEventListener('paste', handlePaste)
    }
  }, [disableCopyPaste])

  // Disable developer tools
  useEffect(() => {
    if (!disableDevTools) return

    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent F12, Ctrl+Shift+I, Ctrl+Shift+C, Ctrl+U, etc.
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.shiftKey && e.key === 'J') ||
        (e.ctrlKey && e.key === 'u') ||
        (e.ctrlKey && e.key === 's') ||
        (e.ctrlKey && e.key === 'a')
      ) {
        e.preventDefault()
        logViolation({
          type: 'copy_attempt',
          timestamp: Date.now(),
          details: `Blocked keyboard shortcut: ${e.key}`
        })
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [disableDevTools])

  // DevTools detection (basic)
  useEffect(() => {
    if (!disableDevTools) return

    let devtools = { open: false, orientation: null }
    const threshold = 160

    setInterval(() => {
      if (
        window.outerHeight - window.innerHeight > threshold ||
        window.outerWidth - window.innerWidth > threshold
      ) {
        if (!devtools.open) {
          devtools.open = true
          logViolation({
            type: 'copy_attempt',
            timestamp: Date.now(),
            details: 'Developer tools detected'
          })
        }
      } else {
        devtools.open = false
      }
    }, 500)
  }, [disableDevTools])

  const enterFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen()
        setIsFullscreen(true)
      }
    } catch (error) {
      console.error('Failed to enter fullscreen:', error)
    }
  }

  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen()
        setIsFullscreen(false)
      }
    } catch (error) {
      console.error('Failed to exit fullscreen:', error)
    }
  }

  const getSecurityReport = () => {
    return {
      totalViolations: violations.length,
      tabSwitches: tabSwitchCount.current,
      windowBlurs: windowBlurCount.current,
      violations: violations,
      warningCount,
      isFullscreen,
      lastActiveTime: lastActiveTime.current
    }
  }

  const resetViolations = () => {
    setViolations([])
    setWarningCount(0)
    tabSwitchCount.current = 0
    windowBlurCount.current = 0
  }

  return {
    violations,
    warningCount,
    isFullscreen,
    tabSwitchCount: tabSwitchCount.current,
    windowBlurCount: windowBlurCount.current,
    enterFullscreen,
    exitFullscreen,
    getSecurityReport,
    resetViolations
  }
}
