"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaCalendarAlt, FaNewspaper, FaUserGraduate, FaBook, FaMoneyBillWave, FaLaptop } from 'react-icons/fa';
import * as Icons from 'react-icons/fa';

// Define the quick link interface
interface QuickLink {
  id: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  color: string;
  borderColor: string;
  hoverColor: string;
  isActive: boolean;
  order: number;
}

// Fallback quick links in case API fails
const fallbackLinks = [
  {
    id: '1',
    title: 'Admissions',
    description: 'Apply for enrollment or check admission requirements',
    icon: 'FaGraduationCap',
    url: '/website/admissions',
    color: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    hoverColor: 'hover:bg-blue-100 dark:hover:bg-blue-900/30',
    isActive: true,
    order: 0
  },
  {
    id: '2',
    title: 'Calendar',
    description: 'View academic calendar and upcoming events',
    icon: 'FaCalendarAlt',
    url: '/website/academics#calendar',
    color: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    hoverColor: 'hover:bg-green-100 dark:hover:bg-green-900/30',
    isActive: true,
    order: 1
  },
  {
    id: '3',
    title: 'Latest News',
    description: 'Stay updated with school news and announcements',
    icon: 'FaNewspaper',
    url: '/website/news-events',
    color: 'bg-amber-50 dark:bg-amber-900/20',
    borderColor: 'border-amber-200 dark:border-amber-800',
    hoverColor: 'hover:bg-amber-100 dark:hover:bg-amber-900/30',
    isActive: true,
    order: 2
  },
  {
    id: '4',
    title: 'Student Portal',
    description: 'Access grades, assignments, and resources',
    icon: 'FaUserGraduate',
    url: '/dashboard',
    color: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-800',
    hoverColor: 'hover:bg-purple-100 dark:hover:bg-purple-900/30',
    isActive: true,
    order: 3
  },
  {
    id: '5',
    title: 'Curriculum',
    description: 'Explore our academic programs and courses',
    icon: 'FaBook',
    url: '/website/academics#curriculum',
    color: 'bg-red-50 dark:bg-red-900/20',
    borderColor: 'border-red-200 dark:border-red-800',
    hoverColor: 'hover:bg-red-100 dark:hover:bg-red-900/30',
    isActive: true,
    order: 4
  },
  {
    id: '6',
    title: 'Tuition & Fees',
    description: 'View tuition rates and payment information',
    icon: 'FaMoneyBillWave',
    url: '/website/admissions/tuition-fees',
    color: 'bg-teal-50 dark:bg-teal-900/20',
    borderColor: 'border-teal-200 dark:border-teal-800',
    hoverColor: 'hover:bg-teal-100 dark:hover:bg-teal-900/30',
    isActive: true,
    order: 5
  },
  {
    id: '7',
    title: 'Online Exam',
    description: 'Access online examination system for students',
    icon: 'FaLaptop',
    url: '/exam/login',
    color: 'bg-indigo-50 dark:bg-indigo-900/20',
    borderColor: 'border-indigo-200 dark:border-indigo-800',
    hoverColor: 'hover:bg-indigo-100 dark:hover:bg-indigo-900/30',
    isActive: true,
    order: 6
  }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export default function QuickLinks() {
  const [links, setLinks] = useState<QuickLink[]>(fallbackLinks);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch quick links from API
  useEffect(() => {
    const fetchQuickLinks = async () => {
      try {
        const response = await fetch('/api/website-management/quick-links');
        if (response.ok) {
          const data = await response.json();
          // Filter only active quick links
          const activeLinks = data.filter((link: QuickLink) => link.isActive);
          if (activeLinks.length > 0) {
            setLinks(activeLinks);
          }
        }
      } catch (error) {
        console.error('Error fetching quick links:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuickLinks();
  }, []);

  // Dynamically render the icon component
  const renderIcon = (iconName: string) => {
    // @ts-ignore - Icons is a dynamic import
    const IconComponent = Icons[iconName];
    if (IconComponent) {
      return <IconComponent className="text-3xl text-blue-600" />;
    }
    return null;
  };

  if (isLoading) {
    return (
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Quick Links</h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Access important resources and information about our school with these quick links.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {links.map((link) => (
            <motion.div key={link.id} variants={itemVariants}>
              <Link href={link.url}>
                <div className={`h-full p-6 rounded-lg border ${link.borderColor} ${link.color} ${link.hoverColor} transition-colors duration-300 flex flex-col`}>
                  <div className="mb-4">{renderIcon(link.icon)}</div>
                  <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{link.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">{link.description}</p>
                  <div className="mt-auto pt-4">
                    <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">Learn more →</span>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
