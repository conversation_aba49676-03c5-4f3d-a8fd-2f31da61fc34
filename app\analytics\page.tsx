'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { useQuery } from '@tanstack/react-query'
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, AreaChart, Area
} from 'recharts'
import {
  TrendingUp, TrendingDown, Users, GraduationCap, DollarSign, Calendar,
  BookOpen, Award, Target, Activity, Pie<PERSON><PERSON> as PieChartIcon,
  Download, RefreshCw, Alert<PERSON>riangle, CheckCircle
} from 'lucide-react'

// Import DashboardLayout
import DashboardLayout from '@/app/components/DashboardLayout'

// Color schemes for charts
const COLORS = {
  primary: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#6366F1', '#EC4899', '#14B8A6'],
  gradient: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'],
  performance: {
    excellent: '#10B981',
    good: '#3B82F6',
    average: '#F59E0B',
    poor: '#EF4444'
  }
}

interface Class {
  id: string
  name: string
  hrTeacherId?: string
  totalStudents: number
  totalSubjects?: number
  hrTeacher?: {
    id: string
    name: string
    subject?: string
  }
}

interface AnalyticsData {
  overview: {
    totalStudents: number
    totalTeachers: number
    totalClasses: number
    totalRevenue: number
    attendanceRate: number
    averageGrade: number
    growthRate: number
    activeUsers: number
  }
  trends: {
    enrollment: Array<{ month: string; students: number; teachers: number }>
    attendance: Array<{ date: string; rate: number; present: number; absent: number }>
    performance: Array<{ subject: string; average: number; improvement: number }>
    revenue: Array<{ month: string; amount: number; fees: number; other: number }>
  }
  demographics: {
    genderDistribution: Array<{ name: string; value: number; color: string }>
    ageDistribution: Array<{ age: string; count: number }>
    classDistribution: Array<{ class: string; students: number; capacity: number }>
  }
  academic: {
    subjectPerformance: Array<{ subject: string; average: number; students: number; passRate: number }>
    gradeDistribution: Array<{ grade: string; count: number; percentage: number }>
    topPerformers: Array<{ name: string; average: number; class: string }>
    improvementTrends: Array<{ month: string; improvement: number }>
  }
  financial: {
    feeCollection: Array<{ month: string; collected: number; pending: number; total: number }>
    paymentMethods: Array<{ method: string; amount: number; transactions: number }>
    outstandingFees: Array<{ class: string; amount: number; students: number }>
  }
  operational: {
    systemUsage: Array<{ hour: string; users: number; actions: number }>
    moduleUsage: Array<{ module: string; usage: number; users: number }>
    errorRates: Array<{ date: string; errors: number; requests: number }>
  }
}

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [dateRange, setDateRange] = useState('30d')
  const [selectedClass, setSelectedClass] = useState('all')
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Fetch classes for dropdown
  const { data: classes = [], isLoading: isClassesLoading } = useQuery<Class[]>({
    queryKey: ['classes-for-analytics'],
    queryFn: async (): Promise<Class[]> => {
      const response = await fetch('/api/classes')
      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }
      return response.json()
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  })

  // Fetch analytics data
  const { data: analyticsData, isLoading, refetch } = useQuery({
    queryKey: ['analytics', dateRange, selectedClass],
    queryFn: async (): Promise<AnalyticsData> => {
      const params = new URLSearchParams({
        range: dateRange,
        class: selectedClass
      })

      const response = await fetch(`/api/analytics?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  if (!isMounted || isLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading comprehensive analytics...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Enhanced Header */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 p-8 text-white shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold tracking-tight mb-2 bg-gradient-to-r from-white to-indigo-100 bg-clip-text text-transparent">
                Advanced Analytics
              </h1>
              <p className="text-indigo-100 text-lg">
                Comprehensive insights and data-driven intelligence for informed decision making
              </p>
            </div>
            <div className="hidden md:flex items-center gap-4">
              <div className="h-16 w-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        </div>

        {/* Controls Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border-0 p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 3 months</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder={isClassesLoading ? "Loading..." : "Select class"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {isClassesLoading ? (
                    <SelectItem value="loading" disabled>Loading classes...</SelectItem>
                  ) : (
                    classes.map((cls: Class) => (
                      <SelectItem key={cls.id} value={cls.name}>
                        {cls.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>

              <Button onClick={() => refetch()} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>

              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>

        {/* Analytics Content */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border-0 overflow-hidden">
          <div className="p-6">
            {/* Enhanced Analytics Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex justify-center mb-8">
                <TabsList className="grid grid-cols-6 bg-gradient-to-r from-indigo-100 via-purple-100 to-pink-100 p-1 rounded-xl shadow-inner">
                  <TabsTrigger
                    value="overview"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-indigo-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-4 py-2 text-sm"
                  >
                    Overview
                  </TabsTrigger>
                  <TabsTrigger
                    value="academic"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-indigo-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-4 py-2 text-sm"
                  >
                    Academic
                  </TabsTrigger>
                  <TabsTrigger
                    value="students"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-indigo-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-4 py-2 text-sm"
                  >
                    Students
                  </TabsTrigger>
                  <TabsTrigger
                    value="financial"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-indigo-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-4 py-2 text-sm"
                  >
                    Financial
                  </TabsTrigger>
                  <TabsTrigger
                    value="operational"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-indigo-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-4 py-2 text-sm"
                  >
                    Operational
                  </TabsTrigger>
                  <TabsTrigger
                    value="insights"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-indigo-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-4 py-2 text-sm"
                  >
                    Insights
                  </TabsTrigger>
                </TabsList>
              </div>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* KPI Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="border-0 shadow-xl bg-gradient-to-br from-blue-50 via-blue-100 to-indigo-100 hover:shadow-2xl transition-all duration-300 rounded-2xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-700 text-sm font-semibold">Total Students</p>
                        <p className="text-4xl font-bold text-blue-900 mt-2">
                          {analyticsData?.overview.totalStudents?.toLocaleString() || 0}
                        </p>
                        <div className="flex items-center mt-3">
                          {(analyticsData?.overview.growthRate || 0) >= 0 ? (
                            <TrendingUp className="h-4 w-4 text-green-600 mr-2" />
                          ) : (
                            <TrendingDown className="h-4 w-4 text-red-600 mr-2" />
                          )}
                          <span className={`text-sm font-medium ${(analyticsData?.overview.growthRate || 0) >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                            {(analyticsData?.overview.growthRate || 0) >= 0 ? '+' : ''}{analyticsData?.overview.growthRate || 0}% this month
                          </span>
                        </div>
                      </div>
                      <div className="p-4 bg-blue-500/20 backdrop-blur-sm rounded-2xl">
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-600 dark:text-green-400 text-sm font-medium">Attendance Rate</p>
                        <p className="text-3xl font-bold text-green-900 dark:text-green-100">
                          {analyticsData?.overview.attendanceRate || 0}%
                        </p>
                        <div className="flex items-center mt-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm text-green-600 dark:text-green-400">
                            Excellent performance
                          </span>
                        </div>
                      </div>
                      <div className="p-3 bg-green-500 rounded-full">
                        <Calendar className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-purple-600 dark:text-purple-400 text-sm font-medium">Average Grade</p>
                        <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
                          {analyticsData?.overview.averageGrade || 0}%
                        </p>
                        <div className="flex items-center mt-2">
                          <Award className="h-4 w-4 text-purple-500 mr-1" />
                          <span className="text-sm text-purple-600 dark:text-purple-400">
                            Above target
                          </span>
                        </div>
                      </div>
                      <div className="p-3 bg-purple-500 rounded-full">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-orange-600 dark:text-orange-400 text-sm font-medium">Total Revenue</p>
                        <p className="text-3xl font-bold text-orange-900 dark:text-orange-100">
                          ${analyticsData?.overview.totalRevenue?.toLocaleString() || 0}
                        </p>
                        <div className="flex items-center mt-2">
                          <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm text-green-600 dark:text-green-400">
                            +12% vs last month
                          </span>
                        </div>
                      </div>
                      <div className="p-3 bg-orange-500 rounded-full">
                        <DollarSign className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Enrollment Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
                      Enrollment Trends
                    </CardTitle>
                    <CardDescription>Student and teacher enrollment over time</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={analyticsData?.trends.enrollment || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="students"
                          stroke="#3B82F6"
                          strokeWidth={3}
                          dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="teachers"
                          stroke="#10B981"
                          strokeWidth={3}
                          dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Performance Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <PieChartIcon className="h-5 w-5 mr-2 text-purple-500" />
                      Performance Distribution
                    </CardTitle>
                    <CardDescription>Student performance across different grade ranges</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={analyticsData?.academic.gradeDistribution || []}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percentage }) => `${name}: ${percentage}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="count"
                        >
                          {(analyticsData?.academic.gradeDistribution || []).map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS.primary[index % COLORS.primary.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Academic Tab */}
            <TabsContent value="academic" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Subject Performance */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BookOpen className="h-5 w-5 mr-2 text-blue-500" />
                      Subject Performance
                    </CardTitle>
                    <CardDescription>Average performance across all subjects</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={analyticsData?.academic.subjectPerformance || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="subject" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="average" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Top Performers */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Award className="h-5 w-5 mr-2 text-yellow-500" />
                      Top Performers
                    </CardTitle>
                    <CardDescription>Students with highest academic performance</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {(analyticsData?.academic.topPerformers || []).slice(0, 5).map((student, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                              index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                            }`}>
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium">{student.name}</p>
                              <p className="text-sm text-gray-500">{student.class}</p>
                            </div>
                          </div>
                          <Badge variant="secondary">{student.average}%</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Students Tab */}
            <TabsContent value="students" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Gender Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Gender Distribution</CardTitle>
                    <CardDescription>Student demographics by gender</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <PieChart>
                        <Pie
                          data={analyticsData?.demographics.genderDistribution || []}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                        >
                          {(analyticsData?.demographics.genderDistribution || []).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Age Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Age Distribution</CardTitle>
                    <CardDescription>Students by age groups</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <BarChart data={analyticsData?.demographics.ageDistribution || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="age" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="count" fill="#10B981" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Class Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Class Distribution</CardTitle>
                    <CardDescription>Students per class vs capacity</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <BarChart data={analyticsData?.demographics.classDistribution || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="class" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="students" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="capacity" fill="#E5E7EB" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Financial Tab */}
            <TabsContent value="financial" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Fee Collection Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                      Fee Collection Trends
                    </CardTitle>
                    <CardDescription>Monthly fee collection vs targets</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={analyticsData?.financial.feeCollection || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Area type="monotone" dataKey="collected" stackId="1" stroke="#10B981" fill="#10B981" />
                        <Area type="monotone" dataKey="pending" stackId="1" stroke="#F59E0B" fill="#F59E0B" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Payment Methods */}
                <Card>
                  <CardHeader>
                    <CardTitle>Payment Methods</CardTitle>
                    <CardDescription>Distribution of payment methods used</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={analyticsData?.financial.paymentMethods || []}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="amount"
                          label={({ method, amount }) => `${method}: $${amount.toLocaleString()}`}
                        >
                          {(analyticsData?.financial.paymentMethods || []).map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS.primary[index % COLORS.primary.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Operational Tab */}
            <TabsContent value="operational" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* System Usage */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="h-5 w-5 mr-2 text-purple-500" />
                      System Usage Patterns
                    </CardTitle>
                    <CardDescription>Daily system usage by hour</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={analyticsData?.operational.systemUsage || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="users" stroke="#8B5CF6" strokeWidth={3} />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Module Usage */}
                <Card>
                  <CardHeader>
                    <CardTitle>Module Usage</CardTitle>
                    <CardDescription>Most used application modules</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={analyticsData?.operational.moduleUsage || []} layout="horizontal">
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis dataKey="module" type="category" width={80} />
                        <Tooltip />
                        <Bar dataKey="usage" fill="#F59E0B" radius={[0, 4, 4, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Insights Tab */}
            <TabsContent value="insights" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Key Insights */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Target className="h-5 w-5 mr-2 text-indigo-500" />
                      Key Insights & Recommendations
                    </CardTitle>
                    <CardDescription>AI-powered insights from your data</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
                        <div className="flex items-start space-x-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-green-800 dark:text-green-200">Excellent Attendance</h4>
                            <p className="text-sm text-green-600 dark:text-green-300 mt-1">
                              Overall attendance rate is 94%, which is above the target of 90%.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                        <div className="flex items-start space-x-3">
                          <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-blue-800 dark:text-blue-200">Growing Enrollment</h4>
                            <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                              Student enrollment has increased by 8% this semester compared to last year.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
                        <div className="flex items-start space-x-3">
                          <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-yellow-800 dark:text-yellow-200">Fee Collection Alert</h4>
                            <p className="text-sm text-yellow-600 dark:text-yellow-300 mt-1">
                              15% of students have pending fee payments. Consider sending reminders.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Trends</CardTitle>
                    <CardDescription>Academic improvement over time</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={analyticsData?.academic.improvementTrends || []}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Line
                          type="monotone"
                          dataKey="improvement"
                          stroke="#10B981"
                          strokeWidth={3}
                          dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
