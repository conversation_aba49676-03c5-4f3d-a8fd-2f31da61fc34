'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { But<PERSON> } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Progress } from '@/app/components/ui/progress'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { 
  ArrowLeft,
  Download,
  Search,
  RefreshCw,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Users,
  BarChart3,
  TrendingUp,
  Award,
  FileText
} from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts'
import Link from 'next/link'

interface ExamAnalysis {
  exam: {
    id: string
    title: string
    description?: string
    duration: number
    totalMarks: number
    passingMarks: number
    class: { id: string; name: string }
    subject: { id: string; name: string }
    totalQuestions: number
    isActive: boolean
    createdAt: string
  }
  statistics: {
    totalResults: number
    passedResults: number
    failedResults: number
    averageScore: number
    passRate: number
    gradeDistribution: { [key: string]: number }
    timeAnalysis: {
      averageTime: number
      minTime: number
      maxTime: number
      examDuration: number
    }
  }
  questionAnalysis: Array<{
    questionId: string
    questionText: string
    questionType: string
    difficulty: string
    correctAnswer: string
    marks: number
    totalAttempts: number
    correctAttempts: number
    wrongAttempts: number
    successRate: number
    averageTimeSpent: number
    commonWrongAnswers: Array<{ answer: string; count: number }>
  }>
  results: Array<{
    id: string
    student: {
      id: string
      name: string
      sid: string
      email: string
      className: string
    }
    marksObtained: number
    totalMarks: number
    percentage: number
    grade: string
    isPassed: boolean
    correctAnswers: number
    wrongAnswers: number
    totalQuestions: number
    attemptedQuestions: number
    timeTaken: number
    submittedAt: string
    responses: Array<{
      questionId: string
      questionText: string
      selectedAnswer: string
      correctAnswer: string
      isCorrect: boolean
      timeSpent: number
    }>
  }>
}

export default function ExamResultsDetailPage() {
  const params = useParams()
  const router = useRouter()
  const examId = params.examId as string
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch exam analysis data
  const { data: examAnalysis, isLoading, error, refetch } = useQuery({
    queryKey: ['exam-analysis', examId],
    queryFn: async (): Promise<ExamAnalysis> => {
      const response = await fetch(`/api/exam-results/${examId}`)
      if (!response.ok) throw new Error('Failed to fetch exam analysis')
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    if (score >= 60) return 'text-orange-600'
    return 'text-red-600'
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+': case 'A': return 'bg-green-100 text-green-800'
      case 'B+': case 'B': return 'bg-blue-100 text-blue-800'
      case 'C+': case 'C': return 'bg-yellow-100 text-yellow-800'
      case 'D': return 'bg-orange-100 text-orange-800'
      case 'F': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Filter results based on search term
  const filteredResults = examAnalysis?.results.filter(result =>
    result.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    result.student.sid.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C']

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading exam analysis...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !examAnalysis) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/online-exam/results">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Results
              </Button>
            </Link>
          </div>
          <Alert>
            <AlertDescription>
              Failed to load exam analysis. Please try again.
            </AlertDescription>
          </Alert>
          <Button onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/online-exam/results">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Results
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {examAnalysis.exam.title}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {examAnalysis.exam.subject.name} • {examAnalysis.exam.class.name}
              </p>
              <p className="text-sm text-gray-500">
                Created {formatDate(examAnalysis.exam.createdAt)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Results
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-700 text-sm font-medium">Total Results</p>
                  <p className="text-3xl font-bold text-blue-900 mt-2">
                    {examAnalysis.statistics.totalResults}
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    {examAnalysis.exam.totalQuestions} questions
                  </p>
                </div>
                <Users className="h-12 w-12 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-700 text-sm font-medium">Pass Rate</p>
                  <p className="text-3xl font-bold text-green-900 mt-2">
                    {Math.round(examAnalysis.statistics.passRate)}%
                  </p>
                  <p className="text-green-600 text-sm mt-1">
                    {examAnalysis.statistics.passedResults} passed
                  </p>
                </div>
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-700 text-sm font-medium">Average Score</p>
                  <p className="text-3xl font-bold text-purple-900 mt-2">
                    {Math.round(examAnalysis.statistics.averageScore)}%
                  </p>
                  <p className="text-purple-600 text-sm mt-1">
                    {Math.round((examAnalysis.statistics.averageScore / 100) * examAnalysis.exam.totalMarks)} marks
                  </p>
                </div>
                <Target className="h-12 w-12 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-700 text-sm font-medium">Avg Time</p>
                  <p className="text-3xl font-bold text-orange-900 mt-2">
                    {Math.round(examAnalysis.statistics.timeAnalysis.averageTime / 60)}m
                  </p>
                  <p className="text-orange-600 text-sm mt-1">
                    of {examAnalysis.exam.duration}m allowed
                  </p>
                </div>
                <Clock className="h-12 w-12 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="results" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="results">Student Results</TabsTrigger>
            <TabsTrigger value="questions">Question Analysis</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          {/* Student Results Tab */}
          <TabsContent value="results" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Student Results</CardTitle>
                    <CardDescription>Individual student performance</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <Input
                        placeholder="Search students..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-64"
                      />
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredResults.map((result) => (
                    <div key={result.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`p-3 rounded-full ${result.isPassed ? 'bg-green-100' : 'bg-red-100'}`}>
                            {result.isPassed ? (
                              <CheckCircle className="h-6 w-6 text-green-600" />
                            ) : (
                              <XCircle className="h-6 w-6 text-red-600" />
                            )}
                          </div>
                          <div>
                            <h4 className="font-medium">{result.student.name}</h4>
                            <p className="text-sm text-gray-600">ID: {result.student.sid}</p>
                            <p className="text-xs text-gray-500">{result.student.className}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-6">
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Score</p>
                            <p className={`text-xl font-bold ${getScoreColor(result.percentage)}`}>
                              {Math.round(result.percentage)}%
                            </p>
                            <Badge className={getGradeColor(result.grade)}>
                              {result.grade}
                            </Badge>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Marks</p>
                            <p className="font-medium">
                              {result.marksObtained}/{result.totalMarks}
                            </p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Time</p>
                            <p className="font-medium">{formatTime(result.timeTaken)}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Correct</p>
                            <p className="font-medium text-green-600">
                              {result.correctAnswers}/{result.totalQuestions}
                            </p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Submitted</p>
                            <p className="text-xs text-gray-500">{formatDate(result.submittedAt)}</p>
                          </div>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Question Analysis Tab */}
          <TabsContent value="questions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Question Performance Analysis</CardTitle>
                <CardDescription>How students performed on each question</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {examAnalysis.questionAnalysis.map((question, index) => (
                    <div key={question.questionId} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <Badge variant="outline">Q{index + 1}</Badge>
                            <Badge className={
                              question.difficulty === 'EASY' ? 'bg-green-100 text-green-800' :
                              question.difficulty === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }>
                              {question.difficulty}
                            </Badge>
                            <Badge variant="outline">{question.marks} marks</Badge>
                          </div>
                          <p className="text-sm text-gray-900 mb-2">
                            {question.questionText.substring(0, 150)}
                            {question.questionText.length > 150 ? '...' : ''}
                          </p>
                          <p className="text-xs text-gray-600">
                            Correct Answer: <span className="font-medium">{question.correctAnswer}</span>
                          </p>
                        </div>

                        <div className="text-right ml-4">
                          <p className="text-2xl font-bold text-blue-600">
                            {Math.round(question.successRate)}%
                          </p>
                          <p className="text-sm text-gray-600">Success Rate</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <p className="text-lg font-bold text-blue-600">{question.totalAttempts}</p>
                          <p className="text-sm text-blue-700">Total Attempts</p>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <p className="text-lg font-bold text-green-600">{question.correctAttempts}</p>
                          <p className="text-sm text-green-700">Correct</p>
                        </div>
                        <div className="text-center p-3 bg-red-50 rounded-lg">
                          <p className="text-lg font-bold text-red-600">{question.wrongAttempts}</p>
                          <p className="text-sm text-red-700">Wrong</p>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <p className="text-lg font-bold text-purple-600">{question.averageTimeSpent}s</p>
                          <p className="text-sm text-purple-700">Avg Time</p>
                        </div>
                      </div>

                      <Progress value={question.successRate} className="h-2 mb-2" />

                      {question.commonWrongAnswers.length > 0 && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-700 mb-2">Common Wrong Answers:</p>
                          <div className="flex flex-wrap gap-2">
                            {question.commonWrongAnswers.map((wrong, idx) => (
                              <Badge key={idx} variant="outline" className="text-red-600">
                                {wrong.answer} ({wrong.count})
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="statistics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Grade Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Grade Distribution</CardTitle>
                  <CardDescription>Distribution of grades across all students</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={Object.entries(examAnalysis.statistics.gradeDistribution).map(([grade, count]) => ({
                          name: grade,
                          value: count,
                          percentage: Math.round((count / examAnalysis.statistics.totalResults) * 100)
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name} (${percentage}%)`}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(examAnalysis.statistics.gradeDistribution).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Score Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Score Distribution</CardTitle>
                  <CardDescription>Distribution of scores in ranges</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={[
                      { range: '90-100%', count: examAnalysis.results.filter(r => r.percentage >= 90).length },
                      { range: '80-89%', count: examAnalysis.results.filter(r => r.percentage >= 80 && r.percentage < 90).length },
                      { range: '70-79%', count: examAnalysis.results.filter(r => r.percentage >= 70 && r.percentage < 80).length },
                      { range: '60-69%', count: examAnalysis.results.filter(r => r.percentage >= 60 && r.percentage < 70).length },
                      { range: '50-59%', count: examAnalysis.results.filter(r => r.percentage >= 50 && r.percentage < 60).length },
                      { range: '0-49%', count: examAnalysis.results.filter(r => r.percentage < 50).length }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="range" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Time Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Time Analysis</CardTitle>
                <CardDescription>How students utilized the exam time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <p className="text-lg font-bold text-blue-600">
                      {formatTime(examAnalysis.statistics.timeAnalysis.averageTime)}
                    </p>
                    <p className="text-sm text-blue-700">Average Time</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <p className="text-lg font-bold text-green-600">
                      {formatTime(examAnalysis.statistics.timeAnalysis.minTime)}
                    </p>
                    <p className="text-sm text-green-700">Fastest Time</p>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <BarChart3 className="h-8 w-8 text-red-600 mx-auto mb-2" />
                    <p className="text-lg font-bold text-red-600">
                      {formatTime(examAnalysis.statistics.timeAnalysis.maxTime)}
                    </p>
                    <p className="text-sm text-red-700">Slowest Time</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <Target className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <p className="text-lg font-bold text-purple-600">
                      {formatTime(examAnalysis.statistics.timeAnalysis.examDuration)}
                    </p>
                    <p className="text-sm text-purple-700">Time Allowed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Insights Tab */}
          <TabsContent value="insights" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Award className="h-5 w-5 text-yellow-500" />
                    <span>Performance Insights</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Overall Performance</h4>
                    <p className="text-sm text-blue-800">
                      {examAnalysis.statistics.passRate >= 80
                        ? "Excellent performance! Most students passed with good scores."
                        : examAnalysis.statistics.passRate >= 60
                        ? "Good performance overall, but there's room for improvement."
                        : "Performance needs attention. Consider reviewing difficult topics."
                      }
                    </p>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900 mb-2">Time Management</h4>
                    <p className="text-sm text-green-800">
                      {examAnalysis.statistics.timeAnalysis.averageTime < examAnalysis.statistics.timeAnalysis.examDuration * 0.8
                        ? "Students completed the exam efficiently with time to spare."
                        : "Students used most of the allocated time, indicating appropriate difficulty level."
                      }
                    </p>
                  </div>

                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <h4 className="font-medium text-yellow-900 mb-2">Question Difficulty</h4>
                    <p className="text-sm text-yellow-800">
                      {examAnalysis.questionAnalysis.filter(q => q.successRate < 50).length > 0
                        ? `${examAnalysis.questionAnalysis.filter(q => q.successRate < 50).length} questions had low success rates. Consider reviewing these topics.`
                        : "All questions had reasonable success rates, indicating good question balance."
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <span>Recommendations</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {examAnalysis.statistics.passRate < 70 && (
                    <div className="p-4 border-l-4 border-red-500 bg-red-50">
                      <h4 className="font-medium text-red-900">Low Pass Rate</h4>
                      <p className="text-sm text-red-800 mt-1">
                        Consider providing additional study materials or review sessions for struggling students.
                      </p>
                    </div>
                  )}

                  {examAnalysis.questionAnalysis.some(q => q.successRate < 30) && (
                    <div className="p-4 border-l-4 border-yellow-500 bg-yellow-50">
                      <h4 className="font-medium text-yellow-900">Difficult Questions</h4>
                      <p className="text-sm text-yellow-800 mt-1">
                        Some questions had very low success rates. Review these questions for clarity and difficulty.
                      </p>
                    </div>
                  )}

                  {examAnalysis.statistics.timeAnalysis.averageTime > examAnalysis.statistics.timeAnalysis.examDuration * 0.9 && (
                    <div className="p-4 border-l-4 border-blue-500 bg-blue-50">
                      <h4 className="font-medium text-blue-900">Time Pressure</h4>
                      <p className="text-sm text-blue-800 mt-1">
                        Students used most of the allocated time. Consider if more time is needed or if questions should be simplified.
                      </p>
                    </div>
                  )}

                  <div className="p-4 border-l-4 border-green-500 bg-green-50">
                    <h4 className="font-medium text-green-900">Next Steps</h4>
                    <ul className="text-sm text-green-800 mt-1 space-y-1">
                      <li>• Review questions with low success rates</li>
                      <li>• Provide feedback to students on their performance</li>
                      <li>• Consider additional practice for weak areas</li>
                      <li>• Update question bank based on insights</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
