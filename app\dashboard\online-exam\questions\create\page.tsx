'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useMutation, useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Badge } from '@/app/components/ui/badge'
import { useNotification } from '@/app/hooks/useNotification'
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  HelpCircle,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import Link from 'next/link'

interface QuestionOption {
  key: string
  text: string
}

interface CreateQuestionData {
  questionText: string
  questionType: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER'
  options: QuestionOption[]
  correctAnswer: string
  marks: number
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  explanation?: string
  subjectId: string
}

export default function CreateQuestionPage() {
  const router = useRouter()
  const { showSuccess, showError } = useNotification()
  const [formData, setFormData] = useState<CreateQuestionData>({
    questionText: '',
    questionType: 'MULTIPLE_CHOICE',
    options: [
      { key: 'A', text: '' },
      { key: 'B', text: '' },
      { key: 'C', text: '' },
      { key: 'D', text: '' }
    ],
    correctAnswer: '',
    marks: 1,
    difficulty: 'MEDIUM',
    explanation: '',
    subjectId: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Fetch subjects for dropdown
  const { data: subjects, isLoading: subjectsLoading } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await fetch('/api/subjects', {
        credentials: 'include' // Include cookies for authentication
      })
      if (!response.ok) throw new Error('Failed to fetch subjects')
      return response.json()
    }
  })

  // Create question mutation
  const createQuestionMutation = useMutation({
    mutationFn: async (data: CreateQuestionData) => {
      const response = await fetch('/api/questions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(data)
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create question')
      }
      return response.json()
    },
    onSuccess: (data) => {
      showSuccess(
        'Question Created Successfully!',
        `Your ${formData.questionType.replace('_', ' ').toLowerCase()} question has been saved and is now available in the question bank.`
      )
      // Small delay to show the success message before redirecting
      setTimeout(() => {
        router.push('/dashboard/online-exam/questions')
      }, 1000)
    },
    onError: (error: Error) => {
      setErrors({ submit: error.message })
      showError(
        'Failed to Create Question',
        error.message || 'There was an error saving your question. Please check your inputs and try again.'
      )
    }
  })

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.questionText.trim()) {
      newErrors.questionText = 'Question text is required'
    }

    if (!formData.subjectId) {
      newErrors.subjectId = 'Subject is required'
    }

    if (formData.questionType === 'MULTIPLE_CHOICE') {
      const filledOptions = formData.options.filter(opt => opt.text.trim())
      if (filledOptions.length < 2) {
        newErrors.options = 'At least 2 options are required'
      }

      if (!formData.correctAnswer) {
        newErrors.correctAnswer = 'Correct answer must be selected'
      }
    }

    if (formData.questionType === 'TRUE_FALSE') {
      if (!formData.correctAnswer) {
        newErrors.correctAnswer = 'Correct answer must be selected (True or False)'
      }
    }

    if (formData.questionType === 'SHORT_ANSWER') {
      if (!formData.correctAnswer.trim()) {
        newErrors.correctAnswer = 'Correct answer is required for short answer questions'
      }
    }

    if (formData.marks < 1 || formData.marks > 10) {
      newErrors.marks = 'Marks must be between 1 and 10'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      // Filter out empty options
      const filteredOptions = formData.options.filter(opt => opt.text.trim())
      createQuestionMutation.mutate({
        ...formData,
        options: filteredOptions
      })
    }
  }

  const addOption = () => {
    const nextKey = String.fromCharCode(65 + formData.options.length) // A, B, C, D, E, F...
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { key: nextKey, text: '' }]
    }))
  }

  const removeOption = (index: number) => {
    if (formData.options.length > 2) {
      setFormData(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index),
        correctAnswer: prev.correctAnswer === prev.options[index].key ? '' : prev.correctAnswer
      }))
    }
  }

  const updateOption = (index: number, text: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => 
        i === index ? { ...opt, text } : opt
      )
    }))
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800 border-green-300'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'HARD': return 'bg-red-100 text-red-800 border-red-300'
      default: return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3">
              <Link href="/dashboard/online-exam/questions">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Questions
                </Button>
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Create New Question
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Add a new question to the question bank
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Question Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <HelpCircle className="h-5 w-5 text-blue-600" />
                    <span>Question Details</span>
                  </CardTitle>
                  <CardDescription>
                    Enter the question text and basic information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Question Text */}
                  <div>
                    <Label htmlFor="questionText">Question Text *</Label>
                    <Textarea
                      id="questionText"
                      placeholder="Enter your question here..."
                      value={formData.questionText}
                      onChange={(e) => setFormData(prev => ({ ...prev, questionText: e.target.value }))}
                      className="min-h-[100px]"
                    />
                    {errors.questionText && (
                      <p className="text-sm text-red-600 mt-1">{errors.questionText}</p>
                    )}
                  </div>

                  {/* Subject and Type */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="subject">Subject *</Label>
                      <Select 
                        value={formData.subjectId} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select subject" />
                        </SelectTrigger>
                        <SelectContent>
                          {subjects?.map((subject: any) => (
                            <SelectItem key={subject.id} value={subject.id}>
                              {subject.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.subjectId && (
                        <p className="text-sm text-red-600 mt-1">{errors.subjectId}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="questionType">Question Type</Label>
                      <Select
                        value={formData.questionType}
                        onValueChange={(value: any) => {
                          setFormData(prev => ({
                            ...prev,
                            questionType: value,
                            correctAnswer: '',
                            options: value === 'MULTIPLE_CHOICE' ? [
                              { key: 'A', text: '' },
                              { key: 'B', text: '' },
                              { key: 'C', text: '' },
                              { key: 'D', text: '' }
                            ] : []
                          }))
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MULTIPLE_CHOICE">Multiple Choice</SelectItem>
                          <SelectItem value="TRUE_FALSE">True/False</SelectItem>
                          <SelectItem value="SHORT_ANSWER">Short Answer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Marks and Difficulty */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="marks">Marks *</Label>
                      <Input
                        id="marks"
                        type="number"
                        min="1"
                        max="10"
                        value={formData.marks}
                        onChange={(e) => setFormData(prev => ({ ...prev, marks: parseInt(e.target.value) || 1 }))}
                      />
                      {errors.marks && (
                        <p className="text-sm text-red-600 mt-1">{errors.marks}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="difficulty">Difficulty Level</Label>
                      <Select 
                        value={formData.difficulty} 
                        onValueChange={(value: any) => setFormData(prev => ({ ...prev, difficulty: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="EASY">Easy</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HARD">Hard</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Options (for Multiple Choice) */}
              {formData.questionType === 'MULTIPLE_CHOICE' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                        <span>Answer Options</span>
                      </div>
                      <Button type="button" variant="outline" size="sm" onClick={addOption}>
                        <Plus className="h-4 w-4 mr-1" />
                        Add Option
                      </Button>
                    </CardTitle>
                    <CardDescription>
                      Add answer options for this multiple choice question
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {formData.options.map((option, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <Badge variant="outline" className="w-8 h-8 flex items-center justify-center">
                          {option.key}
                        </Badge>
                        <Input
                          placeholder={`Option ${option.key}`}
                          value={option.text}
                          onChange={(e) => updateOption(index, e.target.value)}
                          className="flex-1"
                        />
                        {formData.options.length > 2 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeOption(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    {errors.options && (
                      <p className="text-sm text-red-600">{errors.options}</p>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Correct Answer Section */}
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-green-800">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span>Correct Answer *</span>
                  </CardTitle>
                  <CardDescription className="text-green-700">
                    {formData.questionType === 'MULTIPLE_CHOICE' && 'Select the correct option from the choices above'}
                    {formData.questionType === 'TRUE_FALSE' && 'Choose whether the statement is True or False'}
                    {formData.questionType === 'SHORT_ANSWER' && 'Enter the expected answer for automatic grading'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Multiple Choice Correct Answer */}
                  {formData.questionType === 'MULTIPLE_CHOICE' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-green-800">Select the correct option:</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {formData.options.map((option, index) => (
                          <div key={index} className={`flex items-center space-x-2 p-2 rounded border ${
                            formData.correctAnswer === option.key
                              ? 'bg-green-100 border-green-300'
                              : 'bg-white border-gray-200'
                          }`}>
                            <input
                              type="radio"
                              name="correctAnswer"
                              value={option.key}
                              checked={formData.correctAnswer === option.key}
                              onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                              className="w-4 h-4 text-green-600"
                              disabled={!option.text.trim()}
                            />
                            <Badge variant="outline" className="w-6 h-6 flex items-center justify-center text-xs">
                              {option.key}
                            </Badge>
                            <span className="text-sm flex-1 truncate">
                              {option.text || `Option ${option.key}`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* True/False Correct Answer */}
                  {formData.questionType === 'TRUE_FALSE' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-green-800">Select the correct answer:</Label>
                      <div className="flex space-x-4">
                        <div className={`flex items-center space-x-2 p-3 rounded border ${
                          formData.correctAnswer === 'true'
                            ? 'bg-green-100 border-green-300'
                            : 'bg-white border-gray-200'
                        }`}>
                          <input
                            type="radio"
                            name="correctAnswer"
                            value="true"
                            checked={formData.correctAnswer === 'true'}
                            onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                            className="w-4 h-4 text-green-600"
                          />
                          <Label htmlFor="true" className="text-green-600 font-medium cursor-pointer">✓ True</Label>
                        </div>
                        <div className={`flex items-center space-x-2 p-3 rounded border ${
                          formData.correctAnswer === 'false'
                            ? 'bg-green-100 border-green-300'
                            : 'bg-white border-gray-200'
                        }`}>
                          <input
                            type="radio"
                            name="correctAnswer"
                            value="false"
                            checked={formData.correctAnswer === 'false'}
                            onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                            className="w-4 h-4 text-green-600"
                          />
                          <Label htmlFor="false" className="text-red-600 font-medium cursor-pointer">✗ False</Label>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Short Answer Correct Answer */}
                  {formData.questionType === 'SHORT_ANSWER' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-green-800">Enter the correct answer:</Label>
                      <Textarea
                        placeholder="Type the expected answer here..."
                        value={formData.correctAnswer}
                        onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                        className="min-h-[80px] border-green-200 focus:border-green-400"
                      />
                      <p className="text-xs text-green-600">
                        💡 This will be used for automatic grading. For partial credit, manual review may be needed.
                      </p>
                    </div>
                  )}

                  {errors.correctAnswer && (
                    <Alert variant="destructive" className="mt-3">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{errors.correctAnswer}</AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>



              {/* Explanation */}
              <Card>
                <CardHeader>
                  <CardTitle>Explanation (Optional)</CardTitle>
                  <CardDescription>
                    Provide an explanation for the correct answer
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Explain why this is the correct answer..."
                    value={formData.explanation}
                    onChange={(e) => setFormData(prev => ({ ...prev, explanation: e.target.value }))}
                    className="min-h-[80px]"
                  />
                </CardContent>
              </Card>
            </div>

            {/* Preview Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Question Preview</CardTitle>
                  <CardDescription>
                    Preview how your question will appear
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Question Info */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Marks:</span>
                      <Badge variant="outline">{formData.marks}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Difficulty:</span>
                      <Badge className={getDifficultyColor(formData.difficulty)}>
                        {formData.difficulty}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Type:</span>
                      <Badge variant="outline">{formData.questionType.replace('_', ' ')}</Badge>
                    </div>
                  </div>

                  {/* Question Text Preview */}
                  {formData.questionText && (
                    <div className="border rounded-lg p-3 bg-gray-50">
                      <p className="text-sm font-medium mb-2">Question:</p>
                      <p className="text-sm">{formData.questionText}</p>
                    </div>
                  )}

                  {/* Options Preview */}
                  {formData.questionType === 'MULTIPLE_CHOICE' && formData.options.some(opt => opt.text) && (
                    <div className="border rounded-lg p-3 bg-gray-50">
                      <p className="text-sm font-medium mb-2">Options:</p>
                      <div className="space-y-1">
                        {formData.options.filter(opt => opt.text).map((option) => (
                          <div key={option.key} className="flex items-center space-x-2">
                            <span className={`text-xs px-2 py-1 rounded ${
                              formData.correctAnswer === option.key
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {option.key}
                            </span>
                            <span className="text-sm">{option.text}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* True/False Preview */}
                  {formData.questionType === 'TRUE_FALSE' && (
                    <div className="border rounded-lg p-3 bg-gray-50">
                      <p className="text-sm font-medium mb-2">Options:</p>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className={`text-xs px-2 py-1 rounded ${
                            formData.correctAnswer === 'true'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            ✓
                          </span>
                          <span className="text-sm">True</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`text-xs px-2 py-1 rounded ${
                            formData.correctAnswer === 'false'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            ✗
                          </span>
                          <span className="text-sm">False</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Short Answer Preview */}
                  {formData.questionType === 'SHORT_ANSWER' && formData.correctAnswer && (
                    <div className="border rounded-lg p-3 bg-gray-50">
                      <p className="text-sm font-medium mb-2">Expected Answer:</p>
                      <div className="bg-green-50 border border-green-200 rounded p-2">
                        <p className="text-sm text-green-800">{formData.correctAnswer}</p>
                      </div>
                    </div>
                  )}

                  {/* Explanation Preview */}
                  {formData.explanation && (
                    <div className="border rounded-lg p-3 bg-blue-50">
                      <p className="text-sm font-medium mb-2 text-blue-800">Explanation:</p>
                      <p className="text-sm text-blue-700">{formData.explanation}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Error Display */}
              {errors.submit && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{errors.submit}</AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    {/* Validation Summary */}
                    <div className="text-sm text-blue-700">
                      <p className="font-medium mb-2">Ready to create?</p>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          {formData.questionText.trim() ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                          )}
                          <span className={formData.questionText.trim() ? 'text-green-700' : 'text-gray-500'}>
                            Question text
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {formData.subjectId ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                          )}
                          <span className={formData.subjectId ? 'text-green-700' : 'text-gray-500'}>
                            Subject selected
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {formData.correctAnswer ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                          )}
                          <span className={formData.correctAnswer ? 'text-green-700' : 'text-gray-500'}>
                            Correct answer set
                          </span>
                        </div>
                        {formData.questionType === 'MULTIPLE_CHOICE' && (
                          <div className="flex items-center space-x-2">
                            {formData.options.filter(opt => opt.text.trim()).length >= 2 ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                            )}
                            <span className={formData.options.filter(opt => opt.text.trim()).length >= 2 ? 'text-green-700' : 'text-gray-500'}>
                              At least 2 options
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                      disabled={createQuestionMutation.isPending}
                      size="lg"
                    >
                      <Save className="h-5 w-5 mr-2" />
                      {createQuestionMutation.isPending ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Creating Question...
                        </>
                      ) : (
                        'Create Question'
                      )}
                    </Button>
                    <Link href="/dashboard/online-exam/questions">
                      <Button variant="outline" className="w-full">
                        Cancel
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}
