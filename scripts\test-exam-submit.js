const { PrismaClient } = require('@prisma/client')
const jwt = require('jsonwebtoken')

const prisma = new PrismaClient()
const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

async function testExamSubmit() {
  try {
    console.log('🧪 Testing Exam Submit Functionality...')
    
    // Find the Grade 9 Mathematics Demo Exam
    const exam = await prisma.exam.findFirst({
      where: {
        title: 'Grade 9 Mathematics Demo Exam'
      },
      include: {
        class: true,
        subject: true,
        questions: {
          include: {
            question: true
          }
        }
      }
    })

    if (!exam) {
      console.log('❌ Exam not found')
      return
    }

    // Find a Grade 9 student
    const student = await prisma.student.findFirst({
      where: {
        class: {
          name: 'Grade 9'
        }
      },
      include: {
        class: true
      }
    })

    if (!student) {
      console.log('❌ No Grade 9 student found')
      return
    }

    console.log('📋 Test Setup:')
    console.log(`   Exam: ${exam.title}`)
    console.log(`   Student: ${student.firstName} ${student.lastName}`)
    console.log(`   Questions: ${exam.questions.length}`)

    // Clean up any existing sessions
    await prisma.examSession.deleteMany({
      where: {
        examId: exam.id,
        studentId: student.id
      }
    })

    // Create a test exam session
    console.log('\n🚀 Creating test exam session...')
    const session = await prisma.examSession.create({
      data: {
        examId: exam.id,
        studentId: student.id,
        startTime: new Date(),
        status: 'IN_PROGRESS',
        warningCount: 0
      }
    })

    console.log(`   ✅ Session created: ${session.id}`)

    // Create some test responses
    console.log('\n📝 Creating test responses...')
    const responses = []
    for (let i = 0; i < Math.min(3, exam.questions.length); i++) {
      const examQuestion = exam.questions[i]
      const question = examQuestion.question
      
      // Use correct answer for first question, wrong for others
      const selectedAnswer = i === 0 ? question.correctAnswer : 'Wrong Answer'
      
      const response = await prisma.examResponse.create({
        data: {
          sessionId: session.id,
          questionId: question.id,
          selectedAnswer: selectedAnswer,
          isCorrect: selectedAnswer === question.correctAnswer,
          timeSpent: 30 + Math.floor(Math.random() * 60)
        }
      })
      
      responses.push(response)
      console.log(`   Question ${i + 1}: ${selectedAnswer === question.correctAnswer ? '✅ Correct' : '❌ Wrong'}`)
    }

    // Test the submit API
    console.log('\n🎯 Testing submit API...')
    
    // Create student token
    const studentData = {
      studentId: student.id,
      sid: student.sid,
      className: student.class.name
    }
    
    const token = jwt.sign(studentData, JWT_SECRET, { expiresIn: '24h' })

    try {
      const response = await fetch('http://localhost:3001/api/exam/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          sessionId: session.id
        })
      })
      
      console.log(`📡 Response Status: ${response.status} ${response.statusText}`)
      
      const responseData = await response.json()
      console.log('📄 Response Data:', JSON.stringify(responseData, null, 2))
      
      if (response.ok) {
        console.log('✅ Exam submit successful!')
        console.log(`   Result ID: ${responseData.resultId}`)
        console.log(`   Message: ${responseData.message}`)
        
        if (responseData.results) {
          console.log('📊 Results:')
          console.log(`   Marks: ${responseData.results.marksObtained}/${responseData.results.totalMarks}`)
          console.log(`   Percentage: ${responseData.results.percentage}%`)
          console.log(`   Passed: ${responseData.results.isPassed}`)
          console.log(`   Grade: ${responseData.results.grade}`)
        }
      } else {
        console.log('❌ Exam submit failed!')
        console.log(`   Error: ${responseData.error}`)
        if (responseData.details) {
          console.log(`   Details: ${responseData.details}`)
        }
      }
      
    } catch (fetchError) {
      console.log('❌ Network error:', fetchError.message)
      console.log('   This might indicate the server is not running on port 3001')
    }

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...')
    await prisma.examResult.deleteMany({
      where: { sessionId: session.id }
    })
    await prisma.examResponse.deleteMany({
      where: { sessionId: session.id }
    })
    await prisma.examSession.delete({
      where: { id: session.id }
    })
    console.log('   ✅ Test data cleaned up')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testExamSubmit()
