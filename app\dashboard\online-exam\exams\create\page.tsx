'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useMutation, useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Badge } from '@/app/components/ui/badge'
import { Checkbox } from '@/app/components/ui/checkbox'
import { 
  Plus, 
  Trash2, 
  Save, 
  ArrowLeft, 
  FileText,
  Clock,
  Users,
  Settings,
  Search,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import Link from 'next/link'

interface CreateExamData {
  title: string
  description: string
  classId: string
  subjectId: string
  duration: number
  totalMarks: number
  passingMarks: number
  instructions: string
  startTime: string
  endTime: string
  allowRetake: boolean
  shuffleQuestions: boolean
  showResults: boolean
  selectedQuestions: string[]
}

interface Question {
  id: string
  questionText: string
  questionType: string
  marks: number
  difficulty: string
  subject: { name: string }
}

export default function CreateExamPage() {
  const router = useRouter()
  const [formData, setFormData] = useState<CreateExamData>({
    title: '',
    description: '',
    classId: '',
    subjectId: '',
    duration: 60,
    totalMarks: 0,
    passingMarks: 0,
    instructions: '',
    startTime: '',
    endTime: '',
    allowRetake: false,
    shuffleQuestions: false,
    showResults: true,
    selectedQuestions: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [questionSearch, setQuestionSearch] = useState('')
  const [questionFilters, setQuestionFilters] = useState({
    difficulty: 'all',
    questionType: 'all'
  })
  const [allowManualTotalMarks, setAllowManualTotalMarks] = useState(false)

  // Fetch classes
  const { data: classes } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const response = await fetch('/api/classes')
      if (!response.ok) throw new Error('Failed to fetch classes')
      return response.json()
    }
  })

  // Fetch subjects
  const { data: subjects } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await fetch('/api/subjects')
      if (!response.ok) throw new Error('Failed to fetch subjects')
      return response.json()
    }
  })

  // Fetch questions based on filters
  const { data: questionsData, isLoading: questionsLoading } = useQuery({
    queryKey: ['questions', formData.subjectId, questionSearch, questionFilters],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (formData.subjectId) params.append('subjectId', formData.subjectId)
      if (questionSearch) params.append('search', questionSearch)
      if (questionFilters.difficulty && questionFilters.difficulty !== 'all') params.append('difficulty', questionFilters.difficulty)
      if (questionFilters.questionType && questionFilters.questionType !== 'all') params.append('questionType', questionFilters.questionType)
      params.append('limit', '50')

      const response = await fetch(`/api/questions?${params}`, {
        credentials: 'include' // Include cookies for authentication
      })
      if (!response.ok) throw new Error('Failed to fetch questions')
      return response.json()
    },
    enabled: !!formData.subjectId
  })

  // Create exam mutation
  const createExamMutation = useMutation({
    mutationFn: async (data: CreateExamData) => {
      const response = await fetch('/api/exams', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(data)
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create exam')
      }
      return response.json()
    },
    onSuccess: () => {
      router.push('/dashboard/online-exam/exams')
    },
    onError: (error: Error) => {
      setErrors({ submit: error.message })
    }
  })

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) newErrors.title = 'Title is required'
    if (!formData.classId) newErrors.classId = 'Class is required'
    if (!formData.subjectId) newErrors.subjectId = 'Subject is required'
    if (formData.duration < 1) newErrors.duration = 'Duration must be at least 1 minute'
    if (formData.selectedQuestions.length === 0) newErrors.questions = 'At least one question must be selected'
    if (formData.totalMarks < 1) newErrors.totalMarks = 'Total marks must be at least 1'
    if (formData.passingMarks < 0) newErrors.passingMarks = 'Passing marks cannot be negative'
    if (formData.passingMarks > formData.totalMarks) newErrors.passingMarks = 'Passing marks cannot exceed total marks'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      createExamMutation.mutate(formData)
    }
  }

  const toggleQuestion = (questionId: string, marks: number) => {
    setFormData(prev => {
      const isSelected = prev.selectedQuestions.includes(questionId)
      const newSelected = isSelected
        ? prev.selectedQuestions.filter(id => id !== questionId)
        : [...prev.selectedQuestions, questionId]

      // Recalculate total marks only if manual override is disabled
      const selectedQuestionsData = questionsData?.questions?.filter((q: Question) =>
        newSelected.includes(q.id)
      ) || []
      const calculatedTotalMarks = selectedQuestionsData.reduce((sum: number, q: Question) => sum + q.marks, 0)
      const newTotalMarks = allowManualTotalMarks ? prev.totalMarks : calculatedTotalMarks

      // Only auto-set passing marks if it's currently 0 or if this is the first question being selected
      const shouldUpdatePassingMarks = prev.passingMarks === 0 || (prev.selectedQuestions.length === 0 && newSelected.length > 0)
      const newPassingMarks = shouldUpdatePassingMarks ? Math.floor(newTotalMarks * 0.6) : prev.passingMarks

      return {
        ...prev,
        selectedQuestions: newSelected,
        totalMarks: newTotalMarks,
        passingMarks: newPassingMarks
      }
    })
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800 border-green-300'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'HARD': return 'bg-red-100 text-red-800 border-red-300'
      default: return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/dashboard/online-exam/exams">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Exams
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Create New Exam
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Set up a new online examination
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <span>Basic Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="title">Exam Title *</Label>
                    <Input
                      id="title"
                      placeholder="e.g., Mathematics Mid-term Exam"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    />
                    {errors.title && <p className="text-sm text-red-600 mt-1">{errors.title}</p>}
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Brief description of the exam"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="class">Class *</Label>
                      <Select 
                        value={formData.classId} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, classId: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select class" />
                        </SelectTrigger>
                        <SelectContent>
                          {classes?.map((cls: any) => (
                            <SelectItem key={cls.id} value={cls.id}>
                              {cls.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.classId && <p className="text-sm text-red-600 mt-1">{errors.classId}</p>}
                    </div>

                    <div>
                      <Label htmlFor="subject">Subject *</Label>
                      <Select 
                        value={formData.subjectId} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select subject" />
                        </SelectTrigger>
                        <SelectContent>
                          {subjects?.map((subject: any) => (
                            <SelectItem key={subject.id} value={subject.id}>
                              {subject.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.subjectId && <p className="text-sm text-red-600 mt-1">{errors.subjectId}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Exam Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5 text-purple-600" />
                    <span>Exam Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="duration">Duration (minutes) *</Label>
                      <Input
                        id="duration"
                        type="number"
                        min="1"
                        value={formData.duration}
                        onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 60 }))}
                      />
                      {errors.duration && <p className="text-sm text-red-600 mt-1">{errors.duration}</p>}
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label htmlFor="totalMarks">Total Marks</Label>
                        <button
                          type="button"
                          onClick={() => setAllowManualTotalMarks(!allowManualTotalMarks)}
                          className="text-xs text-blue-600 hover:text-blue-800 underline"
                        >
                          {allowManualTotalMarks ? 'Auto-calculate' : 'Manual override'}
                        </button>
                      </div>
                      <Input
                        id="totalMarks"
                        type="number"
                        min="1"
                        value={formData.totalMarks}
                        onChange={(e) => setFormData(prev => ({ ...prev, totalMarks: parseInt(e.target.value) || 0 }))}
                        readOnly={!allowManualTotalMarks}
                        className={!allowManualTotalMarks ? "bg-gray-50" : ""}
                        placeholder={allowManualTotalMarks ? "Enter total marks" : "Auto-calculated from questions"}
                      />
                      {!allowManualTotalMarks && (
                        <p className="text-xs text-gray-500 mt-1">
                          Automatically calculated from selected questions
                        </p>
                      )}
                      {errors.totalMarks && <p className="text-sm text-red-600 mt-1">{errors.totalMarks}</p>}
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label htmlFor="passingMarks">Passing Marks *</Label>
                        {formData.totalMarks > 0 && (
                          <div className="flex space-x-2 text-xs">
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, passingMarks: Math.floor(prev.totalMarks * 0.4) }))}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              40%
                            </button>
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, passingMarks: Math.floor(prev.totalMarks * 0.5) }))}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              50%
                            </button>
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, passingMarks: Math.floor(prev.totalMarks * 0.6) }))}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              60%
                            </button>
                          </div>
                        )}
                      </div>
                      <Input
                        id="passingMarks"
                        type="number"
                        min="0"
                        max={formData.totalMarks}
                        value={formData.passingMarks}
                        onChange={(e) => setFormData(prev => ({ ...prev, passingMarks: parseInt(e.target.value) || 0 }))}
                        placeholder="Enter passing marks"
                      />
                      {formData.totalMarks > 0 && formData.passingMarks > 0 && (
                        <p className="text-xs text-gray-500 mt-1">
                          {Math.round((formData.passingMarks / formData.totalMarks) * 100)}% of total marks
                        </p>
                      )}
                      {errors.passingMarks && <p className="text-sm text-red-600 mt-1">{errors.passingMarks}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startTime">Start Time</Label>
                      <Input
                        id="startTime"
                        type="datetime-local"
                        value={formData.startTime}
                        onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="endTime">End Time</Label>
                      <Input
                        id="endTime"
                        type="datetime-local"
                        value={formData.endTime}
                        onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="allowRetake"
                        checked={formData.allowRetake}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowRetake: !!checked }))}
                      />
                      <Label htmlFor="allowRetake">Allow retakes</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="shuffleQuestions"
                        checked={formData.shuffleQuestions}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, shuffleQuestions: !!checked }))}
                      />
                      <Label htmlFor="shuffleQuestions">Shuffle questions</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="showResults"
                        checked={formData.showResults}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showResults: !!checked }))}
                      />
                      <Label htmlFor="showResults">Show results after submission</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle>Exam Instructions</CardTitle>
                  <CardDescription>
                    Instructions that will be shown to students before the exam
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Enter exam instructions here..."
                    value={formData.instructions}
                    onChange={(e) => setFormData(prev => ({ ...prev, instructions: e.target.value }))}
                    className="min-h-[120px]"
                  />
                </CardContent>
              </Card>

              {/* Question Selection */}
              {formData.subjectId && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span>Select Questions</span>
                      </div>
                      <Badge variant="outline">
                        {formData.selectedQuestions.length} selected
                      </Badge>
                    </CardTitle>
                    <CardDescription>
                      Choose questions from the question bank
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Question Filters */}
                    <div className="flex flex-wrap gap-3">
                      <div className="flex-1 min-w-[200px]">
                        <Input
                          placeholder="Search questions..."
                          value={questionSearch}
                          onChange={(e) => setQuestionSearch(e.target.value)}
                          className="w-full"
                        />
                      </div>
                      <Select 
                        value={questionFilters.difficulty} 
                        onValueChange={(value) => setQuestionFilters(prev => ({ ...prev, difficulty: value }))}
                      >
                        <SelectTrigger className="w-[130px]">
                          <SelectValue placeholder="Difficulty" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All</SelectItem>
                          <SelectItem value="EASY">Easy</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HARD">Hard</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select 
                        value={questionFilters.questionType} 
                        onValueChange={(value) => setQuestionFilters(prev => ({ ...prev, questionType: value }))}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="MULTIPLE_CHOICE">Multiple Choice</SelectItem>
                          <SelectItem value="TRUE_FALSE">True/False</SelectItem>
                          <SelectItem value="SHORT_ANSWER">Short Answer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Questions List */}
                    <div className="space-y-3 max-h-[400px] overflow-y-auto">
                      {questionsLoading ? (
                        <div className="text-center py-4">Loading questions...</div>
                      ) : questionsData?.questions?.length > 0 ? (
                        questionsData.questions.map((question: Question) => (
                          <div
                            key={question.id}
                            className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                              formData.selectedQuestions.includes(question.id)
                                ? 'border-blue-300 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => toggleQuestion(question.id, question.marks)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <p className="text-sm font-medium line-clamp-2">
                                  {question.questionText}
                                </p>
                                <div className="flex items-center space-x-2 mt-2">
                                  <Badge variant="outline" className="text-xs">
                                    {question.marks} mark{question.marks !== 1 ? 's' : ''}
                                  </Badge>
                                  <Badge className={`text-xs ${getDifficultyColor(question.difficulty)}`}>
                                    {question.difficulty}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {question.questionType.replace('_', ' ')}
                                  </Badge>
                                </div>
                              </div>
                              <div className="ml-3">
                                <Checkbox
                                  checked={formData.selectedQuestions.includes(question.id)}
                                  onChange={() => toggleQuestion(question.id, question.marks)}
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          No questions found. Try adjusting your filters or create new questions.
                        </div>
                      )}
                    </div>

                    {errors.questions && (
                      <p className="text-sm text-red-600">{errors.questions}</p>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Exam Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Exam Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Questions:</span>
                    <Badge variant="outline">{formData.selectedQuestions.length}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Marks:</span>
                    <Badge variant="outline">{formData.totalMarks}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Passing Marks:</span>
                    <Badge variant="outline">{formData.passingMarks}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Duration:</span>
                    <Badge variant="outline">{formData.duration} min</Badge>
                  </div>
                  {formData.totalMarks > 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Pass %:</span>
                      <Badge variant="outline">
                        {Math.round((formData.passingMarks / formData.totalMarks) * 100)}%
                      </Badge>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Error Display */}
              {errors.submit && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{errors.submit}</AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={createExamMutation.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {createExamMutation.isPending ? 'Creating...' : 'Create Exam'}
                </Button>
                <Link href="/dashboard/online-exam/exams">
                  <Button variant="outline" className="w-full">
                    Cancel
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}
