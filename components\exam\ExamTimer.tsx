'use client'

import { useState, useEffect } from 'react'
import { Clock, AlertTriangle } from 'lucide-react'
import { Alert, AlertDescription } from '@/app/components/ui/alert'

interface ExamTimerProps {
  initialTimeRemaining: number // in seconds
  onTimeUp: () => void
  onWarning?: (timeRemaining: number) => void
  warningThresholds?: number[] // in seconds, default [300, 60] (5 min, 1 min)
}

export default function ExamTimer({ 
  initialTimeRemaining, 
  onTimeUp, 
  onWarning,
  warningThresholds = [300, 60] // 5 minutes and 1 minute warnings
}: ExamTimerProps) {
  const [timeRemaining, setTimeRemaining] = useState(initialTimeRemaining)
  const [warningsShown, setWarningsShown] = useState<Set<number>>(new Set())
  const [showWarning, setShowWarning] = useState(false)

  useEffect(() => {
    setTimeRemaining(initialTimeRemaining)
  }, [initialTimeRemaining])

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        const newTime = Math.max(0, prev - 1)
        
        // Check for warnings
        warningThresholds.forEach(threshold => {
          if (newTime <= threshold && !warningsShown.has(threshold) && newTime > 0) {
            setWarningsShown(prev => new Set([...prev, threshold]))
            setShowWarning(true)
            onWarning?.(newTime)
            
            // Hide warning after 5 seconds
            setTimeout(() => setShowWarning(false), 5000)
          }
        })
        
        // Time up
        if (newTime === 0) {
          onTimeUp()
        }
        
        return newTime
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [onTimeUp, onWarning, warningThresholds, warningsShown])

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getTimerColor = (): string => {
    if (timeRemaining <= 60) return 'text-red-600' // Last minute - red
    if (timeRemaining <= 300) return 'text-orange-600' // Last 5 minutes - orange
    if (timeRemaining <= 900) return 'text-yellow-600' // Last 15 minutes - yellow
    return 'text-green-600' // More than 15 minutes - green
  }

  const getBackgroundColor = (): string => {
    if (timeRemaining <= 60) return 'bg-red-50 border-red-200'
    if (timeRemaining <= 300) return 'bg-orange-50 border-orange-200'
    if (timeRemaining <= 900) return 'bg-yellow-50 border-yellow-200'
    return 'bg-green-50 border-green-200'
  }

  const getProgressPercentage = (): number => {
    return initialTimeRemaining > 0 ? (timeRemaining / initialTimeRemaining) * 100 : 0
  }

  return (
    <div className="space-y-4">
      {/* Timer Display */}
      <div className={`p-4 rounded-lg border-2 ${getBackgroundColor()} transition-colors duration-300`}>
        <div className="flex items-center justify-center space-x-2">
          <Clock className={`h-5 w-5 ${getTimerColor()}`} />
          <span className={`text-2xl font-bold font-mono ${getTimerColor()}`}>
            {formatTime(timeRemaining)}
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3 w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-1000 ${
              timeRemaining <= 60 ? 'bg-red-500' :
              timeRemaining <= 300 ? 'bg-orange-500' :
              timeRemaining <= 900 ? 'bg-yellow-500' : 'bg-green-500'
            }`}
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </div>
        
        {/* Time Status */}
        <div className="mt-2 text-center">
          <span className={`text-sm font-medium ${getTimerColor()}`}>
            {timeRemaining <= 60 ? 'Time Almost Up!' :
             timeRemaining <= 300 ? 'Less than 5 minutes remaining' :
             timeRemaining <= 900 ? 'Less than 15 minutes remaining' :
             'Time Remaining'}
          </span>
        </div>
      </div>

      {/* Warning Alert */}
      {showWarning && (
        <Alert variant="destructive" className="animate-pulse">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {timeRemaining <= 60 
              ? 'Only 1 minute remaining! Please submit your exam soon.'
              : timeRemaining <= 300 
              ? '5 minutes remaining! Please review your answers.'
              : 'Time warning: Please manage your time wisely.'
            }
          </AlertDescription>
        </Alert>
      )}

      {/* Time Up Alert */}
      {timeRemaining === 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Time is up! Your exam will be automatically submitted.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

// Additional timer utilities
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`
  }
  return `${minutes}m`
}

export const getTimeStatus = (timeRemaining: number): 'critical' | 'warning' | 'caution' | 'normal' => {
  if (timeRemaining <= 60) return 'critical'
  if (timeRemaining <= 300) return 'warning'
  if (timeRemaining <= 900) return 'caution'
  return 'normal'
}
