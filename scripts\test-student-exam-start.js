const jwt = require('jsonwebtoken')

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

async function testStudentExamStart() {
  try {
    console.log('🧪 Testing Student Exam Start API...')
    
    // Create a test student token (same as the demo student)
    const studentData = {
      studentId: 'demo-student-2024001',
      sid: '2024001',
      className: 'Grade 9'
    }
    
    const token = jwt.sign(studentData, JWT_SECRET, { expiresIn: '24h' })
    console.log('🔑 Generated test token for demo student')
    
    // Test exam ID (Grade 9 Mathematics Demo Exam)
    const examId = 'cmd93h5kh000gv0jsqfeewj17'
    
    console.log('\n📋 Test Parameters:')
    console.log(`   Student ID: ${studentData.studentId}`)
    console.log(`   Student SID: ${studentData.sid}`)
    console.log(`   Class: ${studentData.className}`)
    console.log(`   Exam ID: ${examId}`)
    console.log(`   Token: ${token.substring(0, 50)}...`)
    
    // Test the exam start API
    console.log('\n🚀 Testing exam start API...')
    
    try {
      const response = await fetch('http://localhost:3002/api/exam/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          examId: examId
        })
      })
      
      console.log(`📡 Response Status: ${response.status} ${response.statusText}`)
      
      const responseData = await response.json()
      console.log('📄 Response Data:', JSON.stringify(responseData, null, 2))
      
      if (response.ok) {
        console.log('✅ Exam start successful!')
        console.log(`   Session ID: ${responseData.sessionId}`)
        console.log(`   Time Remaining: ${responseData.timeRemaining} seconds`)
        console.log(`   Message: ${responseData.message}`)
      } else {
        console.log('❌ Exam start failed!')
        console.log(`   Error: ${responseData.error}`)
        if (responseData.details) {
          console.log(`   Details: ${responseData.details}`)
        }
      }
      
    } catch (fetchError) {
      console.log('❌ Network error:', fetchError.message)
      console.log('   This might indicate the server is not running on port 3001')
    }
    
    // Test token verification separately
    console.log('\n🔍 Testing token verification...')
    try {
      const decoded = jwt.verify(token, JWT_SECRET)
      console.log('✅ Token verification successful')
      console.log('   Decoded data:', JSON.stringify(decoded, null, 2))
    } catch (tokenError) {
      console.log('❌ Token verification failed:', tokenError.message)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testStudentExamStart()
