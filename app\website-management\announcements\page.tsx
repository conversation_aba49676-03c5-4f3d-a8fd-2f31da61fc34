'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../../components/Sidebar'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Textarea } from '../../components/ui/textarea'
import { Label } from '../../components/ui/label'
import { Checkbox } from '../../components/ui/checkbox'
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from '../../components/ui/table'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '../../components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Globe, Bell, Plus, MoreHorizontal, Pencil, Trash2,
  Eye, EyeOff, AlertCircle, Calendar
} from 'lucide-react'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'
import { useToast } from '../../components/ui/use-toast'
import { Toaster } from '../../components/ui/toaster'
import { format } from 'date-fns'
import { CalendarIcon } from "@radix-ui/react-icons"
import { Popover, PopoverContent, PopoverTrigger } from "../../components/ui/popover"
import { Calendar as CalendarComponent } from "../../components/ui/calendar"
import { cn } from "../../lib/utils"

// Create a client
const queryClient = new QueryClient()

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

// Type definition for announcement
interface Announcement {
  id: string;
  title: string;
  content: string;
  date: string;
  type: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

function AnnouncementsManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentAnnouncement, setCurrentAnnouncement] = useState<Announcement | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    date: new Date(),
    type: 'important',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch announcements from the API
  const { data: announcements, isLoading, refetch } = useQuery<Announcement[]>({
    queryKey: ['announcements'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/website-management/announcements')
        if (!res.ok) {
          throw new Error('Failed to fetch announcements')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'School Closure Due to Weather',
            content: 'Due to severe weather conditions, the school will be closed on Monday, May 15th. Stay safe!',
            date: new Date('2023-05-15').toISOString(),
            type: 'urgent',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Parent-Teacher Conference',
            content: 'Parent-Teacher conferences will be held on May 20th. Please schedule your appointment online.',
            date: new Date('2023-05-20').toISOString(),
            type: 'important',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '3',
            title: 'Annual Sports Day',
            content: 'Join us for our Annual Sports Day on June 10th. All parents are welcome to attend and cheer for their children.',
            date: new Date('2023-06-10').toISOString(),
            type: 'event',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Add announcement mutation
  const addAnnouncementMutation = useMutation({
    mutationFn: async (newAnnouncement: Omit<Announcement, 'id' | 'createdAt' | 'updatedAt'>) => {
      const res = await fetch('/api/website-management/announcements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAnnouncement),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to add announcement')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsAddDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Announcement added successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add announcement',
        variant: 'destructive',
      })
    }
  })

  // Update announcement mutation
  const updateAnnouncementMutation = useMutation({
    mutationFn: async (updatedAnnouncement: Partial<Announcement> & { id: string }) => {
      const res = await fetch(`/api/website-management/announcements/${updatedAnnouncement.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedAnnouncement),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update announcement')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] })
      setIsEditDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Announcement updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update announcement',
        variant: 'destructive',
      })
    }
  })

  // Delete announcement mutation
  const deleteAnnouncementMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/website-management/announcements/${id}`, {
        method: 'DELETE',
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete announcement')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsDeleteDialogOpen(false)
      toast({
        title: 'Success',
        description: 'Announcement deleted successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete announcement',
        variant: 'destructive',
      })
    }
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      date: new Date(),
      type: 'important',
      isActive: true
    })
    setCurrentAnnouncement(null)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }))
  }

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({ ...prev, date }))
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  const handleAddAnnouncement = () => {
    addAnnouncementMutation.mutate({
      title: formData.title,
      content: formData.content,
      date: formData.date.toISOString(),
      type: formData.type,
      isActive: formData.isActive
    })
  }

  const handleUpdateAnnouncement = () => {
    if (!currentAnnouncement) return

    updateAnnouncementMutation.mutate({
      id: currentAnnouncement.id,
      title: formData.title,
      content: formData.content,
      date: formData.date.toISOString(),
      type: formData.type,
      isActive: formData.isActive
    })
  }

  const handleDeleteAnnouncement = () => {
    if (!currentAnnouncement) return

    deleteAnnouncementMutation.mutate(currentAnnouncement.id)
  }

  const openEditDialog = (announcement: Announcement) => {
    setCurrentAnnouncement(announcement)
    setFormData({
      title: announcement.title,
      content: announcement.content,
      date: new Date(announcement.date),
      type: announcement.type,
      isActive: announcement.isActive
    })
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (announcement: Announcement) => {
    setCurrentAnnouncement(announcement)
    setIsDeleteDialogOpen(true)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'important':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
      case 'event':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    }
  }

  const getTypeLabel = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1)
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        <Toaster />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Announcements Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the announcements displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Website</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add Announcement</span>
              </Button>
            </div>
          </div>

          <Card className="overflow-hidden border-0 shadow-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">Date</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead className="w-[300px]">Content</TableHead>
                  <TableHead className="w-[120px]">Type</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {announcements && announcements.length > 0 ? (
                  announcements.map((announcement) => (
                    <TableRow key={announcement.id}>
                      <TableCell className="font-medium">
                        {format(new Date(announcement.date), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell>{announcement.title}</TableCell>
                      <TableCell className="max-w-[300px] truncate">{announcement.content}</TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(announcement.type)}`}>
                          {getTypeLabel(announcement.type)}
                        </span>
                      </TableCell>
                      <TableCell>
                        {announcement.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Inactive
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(announcement)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(announcement)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => updateAnnouncementMutation.mutate({ id: announcement.id, isActive: !announcement.isActive })}>
                              {announcement.isActive ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <Bell className="h-12 w-12 mb-2 opacity-50" />
                        <p>No announcements found</p>
                        <Button
                          variant="link"
                          onClick={() => setIsAddDialogOpen(true)}
                          className="mt-2"
                        >
                          Add your first announcement
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>
        </div>
      </main>

      {/* Add Announcement Dialog */}
      <RecursionSafeDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        maxWidth="max-w-[600px]"
      >
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Add New Announcement</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Create a new announcement for the website. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter announcement title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.date ? format(formData.date, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={formData.date}
                    onSelect={handleDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              placeholder="Enter announcement content"
              rows={4}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select value={formData.type} onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="important">Important</SelectItem>
                  <SelectItem value="event">Event</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end space-x-2 h-full pb-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={handleCheckboxChange}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddAnnouncement} disabled={addAnnouncementMutation.isPending}>
            {addAnnouncementMutation.isPending ? 'Adding...' : 'Add Announcement'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Edit Announcement Dialog */}
      <RecursionSafeDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        maxWidth="max-w-[600px]"
      >
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Edit Announcement</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Update the announcement information. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Title</Label>
              <Input
                id="edit-title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter announcement title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-date">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.date ? format(formData.date, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={formData.date}
                    onSelect={handleDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-content">Content</Label>
            <Textarea
              id="edit-content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              placeholder="Enter announcement content"
              rows={4}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-type">Type</Label>
              <Select value={formData.type} onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="important">Important</SelectItem>
                  <SelectItem value="event">Event</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end space-x-2 h-full pb-2">
              <Checkbox
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={handleCheckboxChange}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleUpdateAnnouncement} disabled={updateAnnouncementMutation.isPending}>
            {updateAnnouncementMutation.isPending ? 'Updating...' : 'Update Announcement'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Delete Confirmation Dialog */}
      <RecursionSafeDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        maxWidth="max-w-[425px]"
      >
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Confirm Deletion</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to delete this announcement? This action cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
            <AlertCircle className="h-5 w-5" />
            <p className="text-sm">This will permanently remove the announcement from the website.</p>
          </div>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteAnnouncement}
            disabled={deleteAnnouncementMutation.isPending}
          >
            {deleteAnnouncementMutation.isPending ? 'Deleting...' : 'Delete Announcement'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function AnnouncementsManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <AnnouncementsManagement />
    </QueryClientProvider>
  )
}
