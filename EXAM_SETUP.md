# Online Exam System Setup

## 🎯 Quick Start

### Demo Student Credentials
- **Student ID:** `2024001`
- **Password:** `**********`

### Access Points

#### For Students:
1. **Website Navigation:** Go to main website → Click "Online Exam" in header
2. **Quick Links:** Homepage → Click "Online Exam" card
3. **Direct URL:** `/exam/login`

#### For Administrators:
1. **Dashboard:** Login to admin dashboard → Sidebar → "Online Exam"
2. **Direct URL:** `/dashboard/online-exam`

## 🚀 Setup Instructions

### 1. Seed Demo Data
```bash
npm run seed:exam-demo
```
This will create:
- Demo student (SID: 2024001)
- Demo class (Grade 9)
- Demo subject (Mathematics)
- Sample exam with 5 questions
- All necessary relationships

### 2. Test Authentication
```bash
npm run test:exam-auth
```
This will verify:
- Student login functionality
- Token generation and verification
- Available exams retrieval

### 3. Start Development Server
```bash
npm run dev
```

## 📚 Exam System Features

### For Students:
- **Secure Login:** Student ID + Password authentication
- **Exam Selection:** View available exams for your class
- **Instructions:** Read exam rules before starting
- **Exam Interface:** 
  - Timer with visual countdown
  - Question navigator
  - Flag questions for review
  - Auto-save responses
  - Fullscreen mode
- **Security Features:**
  - Anti-cheating measures
  - Tab switching detection
  - Auto-submit on time expiry
- **Results:** Instant results after submission

### For Administrators:
- **Dashboard:** Overview of exam statistics
- **Exam Management:** Create, edit, and manage exams
- **Question Bank:** Build and organize questions
- **Live Monitoring:** Track active exam sessions
- **Results & Analytics:** View detailed performance reports

## 🔐 Authentication Flow

### Student Login Process:
1. Enter Student ID and Password
2. System validates credentials
3. JWT token generated for session
4. Redirect to exam selection page

### Password Pattern:
- Default: `{StudentID}123`
- Example: SID `2024001` → Password `**********`

## 📝 Demo Exam Details

### Mathematics Demo Exam:
- **Duration:** 30 minutes
- **Questions:** 5 multiple-choice questions
- **Total Marks:** 9 points
- **Passing Marks:** 5 points (55%)
- **Topics:** Basic arithmetic, algebra, geometry
- **Features:** 
  - Retakes allowed
  - Results shown immediately
  - Questions not shuffled (for demo)

### Sample Questions:
1. What is 2 + 2? (1 mark)
2. What is the square root of 16? (2 marks)
3. Solve for x: 2x + 5 = 13 (3 marks)
4. What is the value of π approximately? (1 mark)
5. Triangle with 60°, 60°, 60° angles type? (2 marks)

## 🛠️ Technical Details

### API Endpoints:
- `POST /api/exam/auth` - Student login
- `GET /api/exam/auth` - Token verification
- `GET /api/exam/available` - Get available exams
- `POST /api/exam/start` - Start exam session
- `GET /api/exam/[examId]/questions` - Get exam questions
- `POST /api/exam/[examId]/response` - Save responses
- `POST /api/exam/submit` - Submit exam

### Security Features:
- JWT-based authentication
- Session management
- Anti-cheating detection
- Secure response handling
- Time-based auto-submission

### Database Models:
- Student, Class, Subject
- Exam, Question, ExamQuestion
- ExamSession, ExamResponse, ExamResult

## 🎮 Testing the System

### Manual Testing Steps:
1. **Login Test:**
   - Go to `/exam/login`
   - Enter SID: `2024001`, Password: `**********`
   - Should redirect to exam selection

2. **Exam Selection:**
   - Should see "Mathematics Demo Exam"
   - Click "Take Exam" button

3. **Instructions Page:**
   - Read exam instructions
   - Check "I agree" checkbox
   - Click "Start Exam"

4. **Exam Interface:**
   - Answer the 5 questions
   - Test navigation between questions
   - Try flagging questions
   - Submit exam

5. **Results:**
   - View immediate results
   - Check score and grade

### Automated Testing:
```bash
# Test authentication
npm run test:exam-auth

# Check if demo data exists
npx prisma studio
```

## 🔧 Troubleshooting

### Common Issues:

1. **Student not found:**
   - Run `npm run seed:exam-demo`
   - Check database connection

2. **No exams available:**
   - Ensure demo data is seeded
   - Check exam dates and active status

3. **Authentication fails:**
   - Verify credentials: SID=2024001, Password=**********
   - Check JWT_SECRET in environment

4. **Database errors:**
   - Run `npx prisma generate`
   - Check database connection
   - Verify schema is up to date

### Environment Variables:
```env
JWT_SECRET=your-secret-key
DATABASE_URL=your-database-url
```

## 📞 Support

For issues or questions:
1. Check the console logs for detailed error messages
2. Verify all setup steps have been completed
3. Ensure the development server is running
4. Check database connectivity

## 🎉 Success Indicators

✅ Demo data seeded successfully
✅ Student can login with provided credentials
✅ Exam appears in available exams list
✅ Exam interface loads properly
✅ Questions display correctly
✅ Timer functions properly
✅ Responses are saved
✅ Exam can be submitted
✅ Results are displayed

The system is ready when all indicators show ✅!
