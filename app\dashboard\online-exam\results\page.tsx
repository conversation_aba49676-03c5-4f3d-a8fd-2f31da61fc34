'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Badge } from '@/app/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Progress } from '@/app/components/ui/progress'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  BookOpen, 
  Award,
  Target,
  Calendar,
  Download,
  Filter,
  Search,
  RefreshCw,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Activity
} from 'lucide-react'
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts'

interface AnalyticsData {
  overview: {
    totalExams: number
    activeExams: number
    totalResults: number
    totalStudentsTested: number
    averageScore: number
    passRate: number
    completionRate: number
  }
  performance: {
    subjectWise: Array<{
      subjectName: string
      totalResults: number
      averageScore: number
      passRate: number
    }>
    classWise: Array<{
      className: string
      totalResults: number
      averageScore: number
      passRate: number
    }>
    topPerformers: Array<{
      studentName: string
      studentId: string
      className: string
      examTitle: string
      subject: string
      percentage: number
      marksObtained: number
      totalMarks: number
      submittedAt: string
    }>
    recentActivity: Array<{
      id: string
      studentName: string
      examTitle: string
      subject: string
      className: string
      percentage: number
      isPassed: boolean
      submittedAt: string
    }>
  }
  trends: {
    performanceTrends: Array<{
      date: string
      averageScore: number
      totalExams: number
      passedExams: number
    }>
    difficultyAnalysis: Array<{
      difficulty: string
      totalQuestions: number
      totalAttempts: number
    }>
  }
  exams: Array<{
    id: string
    title: string
    subject: string
    class: string
    isActive: boolean
    totalResults: number
    totalSessions: number
    totalQuestions: number
    createdAt: string
  }>
}

export default function ExamResultsPage() {
  const [filters, setFilters] = useState({
    examId: 'all',
    classId: 'all',
    subjectId: 'all',
    range: '30d'
  })
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch analytics data
  const { data: analyticsData, isLoading, error, refetch } = useQuery({
    queryKey: ['exam-analytics', filters],
    queryFn: async (): Promise<AnalyticsData> => {
      const params = new URLSearchParams()
      if (filters.examId !== 'all') params.append('examId', filters.examId)
      if (filters.classId !== 'all') params.append('classId', filters.classId)
      if (filters.subjectId !== 'all') params.append('subjectId', filters.subjectId)
      params.append('range', filters.range)

      const response = await fetch(`/api/exam-analytics?${params.toString()}`)
      if (!response.ok) throw new Error('Failed to fetch analytics data')
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Fetch subjects and classes for filters
  const { data: subjects } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await fetch('/api/subjects')
      if (!response.ok) throw new Error('Failed to fetch subjects')
      return response.json()
    }
  })

  const { data: classes } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const response = await fetch('/api/classes')
      if (!response.ok) throw new Error('Failed to fetch classes')
      return response.json()
    }
  })

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    if (score >= 60) return 'text-orange-600'
    return 'text-red-600'
  }

  const getGradeFromScore = (score: number) => {
    if (score >= 90) return 'A+'
    if (score >= 80) return 'A'
    if (score >= 70) return 'B+'
    if (score >= 60) return 'B'
    if (score >= 50) return 'C+'
    if (score >= 40) return 'C'
    if (score >= 33) return 'D'
    return 'F'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading analytics data...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <Alert>
            <AlertDescription>
              Failed to load analytics data. Please try again.
            </AlertDescription>
          </Alert>
          <Button onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Results & Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive exam performance analytics and insights
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Time Range</label>
                <Select value={filters.range} onValueChange={(value) => handleFilterChange('range', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                    <SelectItem value="1y">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Class</label>
                <Select value={filters.classId} onValueChange={(value) => handleFilterChange('classId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All classes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Classes</SelectItem>
                    {classes?.classes?.map((cls: any) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Subject</label>
                <Select value={filters.subjectId} onValueChange={(value) => handleFilterChange('subjectId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All subjects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Subjects</SelectItem>
                    {subjects?.subjects?.map((subject: any) => (
                      <SelectItem key={subject.id} value={subject.id}>
                        {subject.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Exam</label>
                <Select value={filters.examId} onValueChange={(value) => handleFilterChange('examId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All exams" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Exams</SelectItem>
                    {analyticsData?.exams?.map((exam) => (
                      <SelectItem key={exam.id} value={exam.id}>
                        {exam.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-700 text-sm font-medium">Total Exams</p>
                  <p className="text-3xl font-bold text-blue-900 mt-2">
                    {analyticsData?.overview.totalExams || 0}
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    {analyticsData?.overview.activeExams || 0} active
                  </p>
                </div>
                <BookOpen className="h-12 w-12 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-700 text-sm font-medium">Students Tested</p>
                  <p className="text-3xl font-bold text-green-900 mt-2">
                    {analyticsData?.overview.totalStudentsTested || 0}
                  </p>
                  <p className="text-green-600 text-sm mt-1">
                    {analyticsData?.overview.totalResults || 0} attempts
                  </p>
                </div>
                <Users className="h-12 w-12 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-700 text-sm font-medium">Average Score</p>
                  <p className="text-3xl font-bold text-purple-900 mt-2">
                    {Math.round(analyticsData?.overview.averageScore || 0)}%
                  </p>
                  <p className="text-purple-600 text-sm mt-1">
                    Grade {getGradeFromScore(analyticsData?.overview.averageScore || 0)}
                  </p>
                </div>
                <Target className="h-12 w-12 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-700 text-sm font-medium">Pass Rate</p>
                  <p className="text-3xl font-bold text-orange-900 mt-2">
                    {Math.round(analyticsData?.overview.passRate || 0)}%
                  </p>
                  <div className="flex items-center mt-1">
                    {(analyticsData?.overview.passRate || 0) >= 70 ? (
                      <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                    )}
                    <p className="text-orange-600 text-sm">
                      {(analyticsData?.overview.passRate || 0) >= 70 ? 'Good' : 'Needs improvement'}
                    </p>
                  </div>
                </div>
                <Award className="h-12 w-12 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="exams">Exams</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Trends Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance Trends</CardTitle>
                  <CardDescription>Average scores over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analyticsData?.trends.performanceTrends || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="averageScore"
                        stroke="#8884d8"
                        strokeWidth={2}
                        name="Average Score"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Subject Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>Subject Performance</CardTitle>
                  <CardDescription>Average scores by subject</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={analyticsData?.performance.subjectWise || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="subjectName" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="averageScore" fill="#8884d8" name="Average Score" />
                      <Bar dataKey="passRate" fill="#82ca9d" name="Pass Rate %" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>Recent Exam Activity</span>
                </CardTitle>
                <CardDescription>Latest exam submissions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData?.performance.recentActivity?.slice(0, 10).map((activity, index) => (
                    <div key={activity.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${activity.isPassed ? 'bg-green-100' : 'bg-red-100'}`}>
                          {activity.isPassed ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{activity.studentName}</p>
                          <p className="text-sm text-gray-600">{activity.examTitle}</p>
                          <p className="text-xs text-gray-500">{activity.subject} • {activity.className}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${getScoreColor(activity.percentage)}`}>
                          {Math.round(activity.percentage)}%
                        </p>
                        <p className="text-xs text-gray-500">{formatDate(activity.submittedAt)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Performers */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    <span>Top Performers</span>
                  </CardTitle>
                  <CardDescription>Highest scoring students</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData?.performance.topPerformers?.slice(0, 10).map((performer, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                            index === 0 ? 'bg-yellow-100 text-yellow-800' :
                            index === 1 ? 'bg-gray-100 text-gray-800' :
                            index === 2 ? 'bg-orange-100 text-orange-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium">{performer.studentName}</p>
                            <p className="text-sm text-gray-600">{performer.examTitle}</p>
                            <p className="text-xs text-gray-500">{performer.className}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-green-600">{Math.round(performer.percentage)}%</p>
                          <p className="text-xs text-gray-500">
                            {performer.marksObtained}/{performer.totalMarks}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Class Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>Class Performance</CardTitle>
                  <CardDescription>Performance breakdown by class</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData?.performance.classWise?.map((classData, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{classData.className}</h4>
                          <Badge variant="outline">{classData.totalResults} results</Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Average Score:</span>
                            <span className={`font-medium ${getScoreColor(classData.averageScore)}`}>
                              {Math.round(classData.averageScore)}%
                            </span>
                          </div>
                          <Progress value={classData.averageScore} className="h-2" />
                          <div className="flex justify-between text-sm">
                            <span>Pass Rate:</span>
                            <span className="font-medium">{Math.round(classData.passRate)}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Subject Performance Detailed */}
            <Card>
              <CardHeader>
                <CardTitle>Subject Performance Analysis</CardTitle>
                <CardDescription>Detailed breakdown by subject</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {analyticsData?.performance.subjectWise?.map((subject, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-3">{subject.subjectName}</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Average Score</span>
                            <span className={`font-medium ${getScoreColor(subject.averageScore)}`}>
                              {Math.round(subject.averageScore)}%
                            </span>
                          </div>
                          <Progress value={subject.averageScore} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Pass Rate</span>
                            <span className="font-medium">{Math.round(subject.passRate)}%</span>
                          </div>
                          <Progress value={subject.passRate} className="h-2" />
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Total Results:</span>
                          <span className="font-medium">{subject.totalResults}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trends Tab */}
          <TabsContent value="trends" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Over Time */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance Over Time</CardTitle>
                  <CardDescription>Daily exam performance trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={analyticsData?.trends.performanceTrends || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="averageScore"
                        stroke="#8884d8"
                        strokeWidth={2}
                        name="Average Score"
                      />
                      <Line
                        type="monotone"
                        dataKey="totalExams"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        name="Total Exams"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Pass/Fail Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Pass/Fail Distribution</CardTitle>
                  <CardDescription>Overall exam results distribution</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <PieChart>
                      <Pie
                        data={[
                          {
                            name: 'Passed',
                            value: Math.round(analyticsData?.overview.passRate || 0),
                            color: '#10B981'
                          },
                          {
                            name: 'Failed',
                            value: Math.round(100 - (analyticsData?.overview.passRate || 0)),
                            color: '#EF4444'
                          }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {[
                          { name: 'Passed', value: analyticsData?.overview.passRate || 0 },
                          { name: 'Failed', value: 100 - (analyticsData?.overview.passRate || 0) }
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={index === 0 ? '#10B981' : '#EF4444'} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Difficulty Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Question Difficulty Analysis</CardTitle>
                <CardDescription>Performance by question difficulty level</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analyticsData?.trends.difficultyAnalysis || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="difficulty" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="totalQuestions" fill="#8884d8" name="Total Questions" />
                    <Bar dataKey="totalAttempts" fill="#82ca9d" name="Total Attempts" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Exams Tab */}
          <TabsContent value="exams" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Exam Overview</CardTitle>
                <CardDescription>All exams with their statistics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData?.exams?.map((exam) => (
                    <div key={exam.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-lg ${exam.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                              <BookOpen className={`h-5 w-5 ${exam.isActive ? 'text-green-600' : 'text-gray-600'}`} />
                            </div>
                            <div>
                              <h4 className="font-medium">{exam.title}</h4>
                              <p className="text-sm text-gray-600">{exam.subject} • {exam.class}</p>
                              <p className="text-xs text-gray-500">
                                Created {formatDate(exam.createdAt)}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-6">
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Results</p>
                            <p className="font-bold">{exam.totalResults}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Sessions</p>
                            <p className="font-bold">{exam.totalSessions}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">Questions</p>
                            <p className="font-bold">{exam.totalQuestions}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={exam.isActive ? "default" : "secondary"}>
                              {exam.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
