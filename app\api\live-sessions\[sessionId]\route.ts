import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

export async function GET(
  request: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to view session details
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { sessionId } = params

    // Fetch detailed session information
    const session = await prisma.examSession.findUnique({
      where: { id: sessionId },
      include: {
        exam: {
          include: {
            class: { select: { id: true, name: true } },
            subject: { select: { id: true, name: true } },
            questions: {
              include: {
                question: {
                  select: {
                    id: true,
                    questionText: true,
                    questionType: true,
                    difficulty: true,
                    options: true,
                    correctAnswer: true
                  }
                }
              },
              orderBy: { order: 'asc' }
            }
          }
        },
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
            className: true
          }
        },
        responses: {
          include: {
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                correctAnswer: true,
                options: true,
                marks: true
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        },
        // warnings: {
        //   orderBy: { createdAt: 'desc' }
        // }
      }
    })

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      )
    }

    // Calculate session statistics
    const currentTime = new Date()
    const timeElapsed = Math.floor((currentTime.getTime() - session.startTime.getTime()) / 1000)
    const timeRemaining = Math.max(0, (session.exam.duration * 60) - timeElapsed)
    const progressPercentage = session.exam.questions.length > 0 
      ? Math.round((session.responses.length / session.exam.questions.length) * 100)
      : 0

    // Calculate current score
    const correctResponses = session.responses.filter(r => r.isCorrect).length
    const currentScore = session.responses.length > 0 
      ? Math.round((correctResponses / session.responses.length) * 100)
      : 0

    // Calculate question-wise progress
    const questionProgress = session.exam.questions.map(examQuestion => {
      const response = session.responses.find(r => r.questionId === examQuestion.question.id)
      return {
        questionId: examQuestion.question.id,
        questionText: examQuestion.question.questionText,
        questionType: examQuestion.question.questionType,
        difficulty: examQuestion.question.difficulty,
        marks: examQuestion.marks,
        order: examQuestion.order,
        isAnswered: !!response,
        selectedAnswer: response?.selectedAnswer || null,
        correctAnswer: examQuestion.question.correctAnswer,
        isCorrect: response?.isCorrect || false,
        timeSpent: response?.timeSpent || 0,
        answeredAt: response?.createdAt || null
      }
    })

    // Calculate activity timeline
    const activityTimeline = [
      {
        type: 'session_start',
        timestamp: session.startTime,
        description: 'Session started',
        icon: 'play'
      },
      ...session.responses.map(response => ({
        type: 'question_answered',
        timestamp: response.createdAt,
        description: `Answered question ${questionProgress.find(q => q.questionId === response.questionId)?.order || '?'}`,
        icon: response.isCorrect ? 'check' : 'x',
        isCorrect: response.isCorrect,
        timeSpent: response.timeSpent
      })),
      // ...session.warnings.map(warning => ({
      //   type: 'warning',
      //   timestamp: warning.createdAt,
      //   description: warning.reason || 'Warning issued',
      //   icon: 'alert',
      //   severity: warning.severity || 'medium'
      // }))
    ].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    // Determine session health and activity status
    let sessionHealth = 'good'
    if (timeRemaining < 300) sessionHealth = 'warning'
    if (timeRemaining === 0) sessionHealth = 'expired'
    if (session.warningCount > 2) sessionHealth = 'suspicious'

    const lastActivity = session.responses.length > 0 
      ? Math.max(...session.responses.map(r => new Date(r.createdAt).getTime()))
      : session.startTime.getTime()
    
    const timeSinceLastActivity = Math.floor((currentTime.getTime() - lastActivity) / 1000)
    let activityStatus = 'active'
    
    if (timeSinceLastActivity > 300) activityStatus = 'idle'
    if (timeSinceLastActivity > 900) activityStatus = 'inactive'

    // Calculate performance metrics
    const answeredQuestions = session.responses.length
    const correctAnswers = correctResponses
    const wrongAnswers = answeredQuestions - correctAnswers
    const unansweredQuestions = session.exam.questions.length - answeredQuestions

    // Calculate time distribution
    const totalTimeSpent = session.responses.reduce((sum, r) => sum + (r.timeSpent || 0), 0)
    const averageTimePerQuestion = answeredQuestions > 0 ? totalTimeSpent / answeredQuestions : 0

    const sessionDetails = {
      session: {
        id: session.id,
        status: session.status,
        startTime: session.startTime,
        endTime: session.endTime,
        isSubmitted: session.isSubmitted,
        warningCount: session.warningCount,
        timeExtension: session.timeExtension || 0,
        terminationReason: session.terminationReason
      },
      student: {
        id: session.student.id,
        name: session.student.name,
        sid: session.student.sid,
        className: session.student.className
      },
      exam: {
        id: session.exam.id,
        title: session.exam.title,
        subject: session.exam.subject.name,
        class: session.exam.class.name,
        duration: session.exam.duration,
        totalQuestions: session.exam.questions.length,
        totalMarks: session.exam.totalMarks,
        passingMarks: session.exam.passingMarks
      },
      statistics: {
        timeElapsed,
        timeRemaining,
        progressPercentage,
        currentScore,
        answeredQuestions,
        correctAnswers,
        wrongAnswers,
        unansweredQuestions,
        totalTimeSpent,
        averageTimePerQuestion,
        sessionHealth,
        activityStatus,
        timeSinceLastActivity
      },
      questionProgress,
      activityTimeline,
      warnings: [] // Warnings not implemented in current schema
    }

    return NextResponse.json(sessionDetails)

  } catch (error) {
    console.error('Error fetching session details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch session details' },
      { status: 500 }
    )
  }
}

// PUT - Update session (extend time, add warning, etc.)
export async function PUT(
  request: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Verify authentication using cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let userData: any

    try {
      userData = await verifyJWT(token.value)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Check if user has permission to update sessions
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { sessionId } = params
    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'extend_time':
        // Time extension not implemented in current schema
        // await prisma.examSession.update({
        //   where: { id: sessionId },
        //   data: {
        //     timeExtension: (data.additionalMinutes || 0)
        //   }
        // })
        break

      case 'add_warning':
        await prisma.examSession.update({
          where: { id: sessionId },
          data: {
            warningCount: {
              increment: 1
            }
          }
        })

        // Create warning record (not implemented in current schema)
        // await prisma.sessionWarning.create({
        //   data: {
        //     sessionId,
        //     reason: data.reason || 'Warning issued',
        //     severity: data.severity || 'medium',
        //     createdBy: userData.id
        //   }
        // })
        break

      case 'terminate':
        await prisma.examSession.update({
          where: { id: sessionId },
          data: {
            status: 'TERMINATED',
            endTime: new Date()
            // terminationReason: data.reason || 'Terminated by administrator' // Not in current schema
          }
        })
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Session updated successfully'
    })

  } catch (error) {
    console.error('Error updating session:', error)
    return NextResponse.json(
      { error: 'Failed to update session' },
      { status: 500 }
    )
  }
}
