import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import { StartExamRequest } from '@/app/types/exam'

const JWT_SECRET = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'

export async function POST(request: Request) {
  try {
    // Verify student authentication
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let studentData: any

    try {
      studentData = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { studentId, className } = studentData
    const body = await request.json()
    const { examId } = body

    console.log(`Starting exam ${examId} for student ${studentId}`)

    // Verify exam exists and is available
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        class: true,
        questions: {
          include: {
            question: true
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    })

    if (!exam) {
      return NextResponse.json(
        { error: 'Exam not found' },
        { status: 404 }
      )
    }

    if (!exam.isActive) {
      return NextResponse.json(
        { error: 'Exam is not active' },
        { status: 400 }
      )
    }

    // Check if student's class matches exam class
    if (exam.class.name !== className) {
      return NextResponse.json(
        { error: 'You are not authorized to take this exam' },
        { status: 403 }
      )
    }

    // Check time restrictions
    const currentTime = new Date()
    if (exam.startTime && exam.startTime > currentTime) {
      return NextResponse.json(
        { error: 'Exam has not started yet' },
        { status: 400 }
      )
    }

    if (exam.endTime && exam.endTime < currentTime) {
      return NextResponse.json(
        { error: 'Exam has ended' },
        { status: 400 }
      )
    }

    // Check if student already has a session for this exam
    const existingSession = await prisma.examSession.findFirst({
      where: {
        examId: examId,
        studentId: studentId
      }
    })

    if (existingSession) {
      if (existingSession.isSubmitted) {
        if (!exam.allowRetake) {
          return NextResponse.json(
            { error: 'You have already completed this exam and retakes are not allowed' },
            { status: 400 }
          )
        }
        // If retakes are allowed, delete the old session and create a new one
        await prisma.examSession.delete({
          where: { id: existingSession.id }
        })
      } else if (existingSession.status === 'IN_PROGRESS') {
        // Return existing session
        const timeElapsed = Math.floor((currentTime.getTime() - existingSession.startTime.getTime()) / 1000)
        const timeRemaining = Math.max(0, (exam.duration * 60) - timeElapsed)

        if (timeRemaining <= 0) {
          // Session has expired, mark as expired and allow new session
          await prisma.examSession.delete({
            where: { id: existingSession.id }
          })
        } else {
          return NextResponse.json({
            sessionId: existingSession.id,
            timeRemaining,
            message: 'Continuing existing exam session'
          })
        }
      } else if (existingSession.status === 'EXPIRED' || existingSession.status === 'COMPLETED') {
        // Delete the old session to allow a new one
        await prisma.examSession.delete({
          where: { id: existingSession.id }
        })
      }
    }

    // Create new exam session
    const session = await prisma.examSession.create({
      data: {
        examId: examId,
        studentId: studentId,
        startTime: currentTime,
        status: 'IN_PROGRESS',
        warningCount: 0
      }
    })

    console.log(`Exam session created: ${session.id}`)

    return NextResponse.json({
      sessionId: session.id,
      timeRemaining: exam.duration * 60,
      message: 'Exam started successfully'
    })

  } catch (error) {
    console.error('Error starting exam:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      examId: body?.examId,
      studentId: studentData?.studentId
    })
    return NextResponse.json(
      {
        error: 'Failed to start exam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
