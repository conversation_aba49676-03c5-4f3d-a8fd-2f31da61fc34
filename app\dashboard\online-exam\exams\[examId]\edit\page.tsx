'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON>, useParams } from 'next/navigation'
import DashboardLayout from '@/app/components/DashboardLayout'
import { Button } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Checkbox } from '@/app/components/ui/checkbox'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { ArrowLeft, Save, Loader2, Calendar, Clock } from 'lucide-react'

interface Class {
  id: string
  name: string
}

interface Subject {
  id: string
  name: string
  classId?: string
}

interface ExamFormData {
  title: string
  description: string
  classId: string
  subjectId: string
  duration: number
  totalMarks: number
  passingMarks: number
  instructions: string
  startTime: string
  endTime: string
  allowRetake: boolean
  shuffleQuestions: boolean
  showResults: boolean
  isActive: boolean
}

export default function EditExamPage() {
  const router = useRouter()
  const params = useParams()
  const examId = params.examId as string

  const [formData, setFormData] = useState<ExamFormData>({
    title: '',
    description: '',
    classId: '',
    subjectId: '',
    duration: 30,
    totalMarks: 0,
    passingMarks: 0,
    instructions: '',
    startTime: '',
    endTime: '',
    allowRetake: false,
    shuffleQuestions: false,
    showResults: true,
    isActive: true
  })

  const [classes, setClasses] = useState<Class[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [allSubjects, setAllSubjects] = useState<Subject[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  useEffect(() => {
    fetchInitialData()
  }, [examId])

  const fetchInitialData = async () => {
    try {
      setLoading(true)
      
      // Fetch exam details, classes, and subjects in parallel
      const [examResponse, classesResponse, subjectsResponse] = await Promise.all([
        fetch(`/api/exams/${examId}`, { credentials: 'include' }),
        fetch('/api/classes', { credentials: 'include' }),
        fetch('/api/subjects', { credentials: 'include' })
      ])

      if (!examResponse.ok) throw new Error('Failed to fetch exam details')
      if (!classesResponse.ok) throw new Error('Failed to fetch classes')
      if (!subjectsResponse.ok) throw new Error('Failed to fetch subjects')

      const [examData, classesData, subjectsData] = await Promise.all([
        examResponse.json(),
        classesResponse.json(),
        subjectsResponse.json()
      ])

      // Set form data from exam
      const exam = examData.exam

      // Helper function to format datetime for input
      const formatDateTimeForInput = (dateString: string | null) => {
        if (!dateString) return ''
        const date = new Date(dateString)
        return date.toISOString().slice(0, 16) // Format: YYYY-MM-DDTHH:mm
      }

      setFormData({
        title: exam.title || '',
        description: exam.description || '',
        classId: exam.class.id || '',
        subjectId: exam.subject.id || '',
        duration: exam.duration || 30,
        totalMarks: exam.totalMarks || 0,
        passingMarks: exam.passingMarks || 0,
        instructions: exam.instructions || '',
        startTime: formatDateTimeForInput(exam.startTime),
        endTime: formatDateTimeForInput(exam.endTime),
        allowRetake: exam.allowRetake || false,
        shuffleQuestions: exam.shuffleQuestions || false,
        showResults: exam.showResults || true,
        isActive: exam.isActive || true
      })

      setClasses(classesData.classes || [])
      setAllSubjects(subjectsData.subjects || [])

      // Filter subjects based on the exam's current class
      const filteredSubjects = (subjectsData.subjects || []).filter(
        (subject: Subject & { classId?: string }) => subject.classId === exam.class.id
      )
      setSubjects(filteredSubjects)

    } catch (error) {
      console.error('Error fetching initial data:', error)
      setError('Failed to load exam data')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof ExamFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleClassChange = (classId: string) => {
    // Update the class in form data
    setFormData(prev => ({
      ...prev,
      classId: classId,
      subjectId: '' // Reset subject when class changes
    }))

    // Filter subjects based on selected class
    const filteredSubjects = allSubjects.filter(
      (subject: Subject & { classId?: string }) => subject.classId === classId
    )
    setSubjects(filteredSubjects)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title || !formData.classId || !formData.subjectId) {
      setError('Please fill in all required fields')
      return
    }

    // Validate start and end times
    if (formData.startTime && formData.endTime) {
      const startDate = new Date(formData.startTime)
      const endDate = new Date(formData.endTime)

      if (endDate <= startDate) {
        setError('End time must be after start time')
        return
      }
    }

    try {
      setSaving(true)
      setError(null)

      // Prepare data for submission
      const submitData = {
        ...formData,
        startTime: formData.startTime ? new Date(formData.startTime).toISOString() : null,
        endTime: formData.endTime ? new Date(formData.endTime).toISOString() : null
      }

      const response = await fetch(`/api/exams/${examId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update exam')
      }

      const result = await response.json()
      console.log('Exam updated successfully:', result.exam.title)

      // Show success message
      setError(null)
      setSuccessMessage('Exam updated successfully! Changes are now live for students.')

      // Small delay to ensure database changes are propagated, then redirect
      setTimeout(() => {
        router.push(`/dashboard/online-exam/exams/${examId}`)
      }, 2000)

    } catch (error: any) {
      console.error('Error updating exam:', error)
      setError(error.message || 'Failed to update exam')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Edit Exam</h1>
          <p className="text-gray-600">Update exam details and settings</p>
        </div>
      </div>

      {error && (
        <Alert>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert className="border-green-200 bg-green-50">
          <div className="flex items-center space-x-2">
            <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <AlertDescription className="text-green-800">{successMessage}</AlertDescription>
          </div>
        </Alert>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Update the basic details of your exam
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Exam Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter exam title"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter exam description (optional)"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="class">Class *</Label>
                    <Select
                      value={formData.classId}
                      onValueChange={handleClassChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select class" />
                      </SelectTrigger>
                      <SelectContent>
                        {classes.map((cls) => (
                          <SelectItem key={cls.id} value={cls.id}>
                            {cls.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="subject">Subject *</Label>
                    <Select
                      value={formData.subjectId}
                      onValueChange={(value) => handleInputChange('subjectId', value)}
                      disabled={!formData.classId || subjects.length === 0}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={
                          !formData.classId
                            ? "Select class first"
                            : subjects.length === 0
                              ? "No subjects available"
                              : "Select subject"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formData.classId && subjects.length === 0 && (
                      <p className="text-xs text-amber-600 mt-1">
                        No subjects found for the selected class. Please contact administrator.
                      </p>
                    )}
                  </div>
                </div>

                {/* Class/Subject Change Warning */}
                {(formData.classId !== (classes.find(c => c.name === 'Grade 9')?.id || '') ||
                  formData.subjectId !== (allSubjects.find(s => s.name === 'Mathematics')?.id || '')) && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-amber-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-amber-800">
                          Class/Subject Change Notice
                        </h3>
                        <div className="mt-2 text-sm text-amber-700">
                          <p>Changing the class or subject will affect student access:</p>
                          <ul className="mt-1 list-disc list-inside space-y-1">
                            <li>Only students in the new class will be able to access this exam</li>
                            <li>Existing exam sessions and results will be preserved</li>
                            <li>Students will see the updated class and subject information</li>
                            <li>The exam will appear in the new class's exam list immediately</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <Label htmlFor="instructions">Instructions</Label>
                  <Textarea
                    id="instructions"
                    value={formData.instructions}
                    onChange={(e) => handleInputChange('instructions', e.target.value)}
                    placeholder="Enter exam instructions for students"
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Exam Schedule */}
            <Card>
              <CardHeader>
                <CardTitle>Exam Schedule</CardTitle>
                <CardDescription>
                  Set exam availability window (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startTime">Start Time</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="startTime"
                        type="datetime-local"
                        value={formData.startTime}
                        onChange={(e) => handleInputChange('startTime', e.target.value)}
                        className="pl-10"
                        placeholder="Select start time"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      When students can start taking the exam
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="endTime">End Time</Label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="endTime"
                        type="datetime-local"
                        value={formData.endTime}
                        onChange={(e) => handleInputChange('endTime', e.target.value)}
                        className="pl-10"
                        placeholder="Select end time"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      When the exam becomes unavailable
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <Calendar className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">Schedule Notes:</p>
                      <ul className="mt-1 space-y-1 text-xs">
                        <li>• Leave empty for no time restrictions</li>
                        <li>• Students can only access the exam within this window</li>
                        <li>• End time should be after start time</li>
                        <li>• Consider exam duration when setting end time</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Exam Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Exam Settings</CardTitle>
                <CardDescription>
                  Configure exam behavior and options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="duration">Duration (minutes) *</Label>
                    <Input
                      id="duration"
                      type="number"
                      min="1"
                      value={formData.duration}
                      onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 0)}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="totalMarks">Total Marks *</Label>
                    <Input
                      id="totalMarks"
                      type="number"
                      min="1"
                      value={formData.totalMarks}
                      onChange={(e) => handleInputChange('totalMarks', parseInt(e.target.value) || 0)}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="passingMarks">Passing Marks *</Label>
                    <Input
                      id="passingMarks"
                      type="number"
                      min="1"
                      max={formData.totalMarks}
                      value={formData.passingMarks}
                      onChange={(e) => handleInputChange('passingMarks', parseInt(e.target.value) || 0)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="allowRetake"
                      checked={formData.allowRetake}
                      onCheckedChange={(checked) => handleInputChange('allowRetake', checked)}
                    />
                    <Label htmlFor="allowRetake">Allow students to retake this exam</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="shuffleQuestions"
                      checked={formData.shuffleQuestions}
                      onCheckedChange={(checked) => handleInputChange('shuffleQuestions', checked)}
                    />
                    <Label htmlFor="shuffleQuestions">Shuffle questions for each student</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="showResults"
                      checked={formData.showResults}
                      onCheckedChange={(checked) => handleInputChange('showResults', checked)}
                    />
                    <Label htmlFor="showResults">Show results to students after submission</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                    />
                    <Label htmlFor="isActive">Exam is active and available to students</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => router.back()}
                >
                  Cancel
                </Button>
              </CardContent>
            </Card>

            {/* Help */}
            <Card>
              <CardHeader>
                <CardTitle>Help</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-gray-600 space-y-2">
                <p>• Total marks will be calculated automatically based on selected questions</p>
                <p>• Passing marks should be less than or equal to total marks</p>
                <p>• Inactive exams won't be visible to students</p>
                <p>• Shuffling questions helps prevent cheating</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
      </div>
    </DashboardLayout>
  )
}
