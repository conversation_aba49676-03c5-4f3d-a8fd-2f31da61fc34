// Define the roles in the system
export type Role = 'SUPER_ADMIN' | 'ADMIN' | 'SUPERVISOR' | 'ACCOUNTANT' | 'TEACHER' | 'PARENT' | 'UNIT_LEADER' | 'DATA_ENCODER';

// Define the page access configuration
// This maps page routes to the roles that can access them
export const pageAccessConfig: Record<string, Role[]> = {
  // Dashboard - accessible to all authenticated users
  '/dashboard': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER'],

  // Student management
  '/students': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
  '/students/add': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'],
  '/students/edit': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'D<PERSON><PERSON>_ENCODER'],
  '/students/id-card': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],

  // Teacher management
  '/teachers': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
  '/teachers/add': ['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'],
  '/teachers/edit': ['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'],

  // Class management
  '/classes': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
  '/classes/add': ['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'],
  '/classes/edit': ['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'],

  // Attendance management
  '/attendance': ['SUPER_ADMIN', 'ADMIN', 'TEACHER', 'UNIT_LEADER'],
  '/attendance/add': ['SUPER_ADMIN', 'ADMIN', 'TEACHER', 'UNIT_LEADER'],
  '/attendance/edit': ['SUPER_ADMIN', 'ADMIN', 'TEACHER', 'UNIT_LEADER'],

  // Mark list management
  '/marklist': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
  '/marklist/add': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'],
  '/marklist/edit': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'],

  // Report card management
  '/reportcard': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/reportcard/generate': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/reportcard/view': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],

  // Online Exam System
  '/dashboard/online-exam': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'],
  '/dashboard/online-exam/exams': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'],
  '/dashboard/online-exam/exams/create': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'],
  '/dashboard/online-exam/questions': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'],
  '/dashboard/online-exam/questions/create': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'],
  '/dashboard/online-exam/results': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER'],
  '/dashboard/online-exam/sessions': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],

  // Role management
  '/roles': ['SUPER_ADMIN'],
  '/roles/add': ['SUPER_ADMIN'],
  '/roles/edit': ['SUPER_ADMIN'],

  // Account reset
  '/account-reset': ['SUPER_ADMIN'],

  // Visualizer
  '/visualizer': ['SUPER_ADMIN', 'SUPERVISOR', 'UNIT_LEADER'],

  // Analytics - Super Admin only
  '/analytics': ['SUPER_ADMIN'],

  // Accounting
  '/accounting': ['SUPER_ADMIN', 'ACCOUNTANT'],
  '/accounting/fee-types': ['SUPER_ADMIN', 'ACCOUNTANT'],
  '/accounting/account-codes': ['SUPER_ADMIN', 'ACCOUNTANT'],
  '/accounting/fee-invoice': ['SUPER_ADMIN', 'ACCOUNTANT'],
  '/accounting/invoices-report': ['SUPER_ADMIN', 'ACCOUNTANT'],
  '/accounting/bank-payment-voucher': ['SUPER_ADMIN', 'ACCOUNTANT'],
  '/accounting/journal-voucher': ['SUPER_ADMIN', 'ACCOUNTANT'],

  // Website management
  '/website-management': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/hero-slides': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/announcements': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/quick-links': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/featured-content': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/news-events': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/academic-calendar': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/testimonials': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
  '/website-management/tuition-fees': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],

  // Parent portal
  '/parent': ['PARENT'],
  '/parent/children': ['PARENT'],
  '/parent/attendance': ['PARENT'],
  '/parent/marks': ['PARENT'],
  '/parent/reports': ['PARENT'],

  // Profile management - accessible to all authenticated users
  '/profile': ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER'],

  // Settings
  '/settings': ['SUPER_ADMIN']
};

// Helper function to check if a user role has access to a page
export function hasPageAccess(path: string, role: string): boolean {
  // Super Admin has access to all pages
  if (role === 'SUPER_ADMIN') return true;

  // Find the most specific path that matches
  const matchingPath = Object.keys(pageAccessConfig)
    .filter(configPath =>
      path === configPath ||
      path.startsWith(`${configPath}/`)
    )
    .sort((a, b) => b.length - a.length)[0]; // Sort by length descending to get most specific match

  // If no matching path found, deny access
  if (!matchingPath) return false;

  // Check if the user's role is in the list of allowed roles for this path
  return pageAccessConfig[matchingPath].includes(role as Role);
}
